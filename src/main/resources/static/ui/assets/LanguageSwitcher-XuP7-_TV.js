import{d as C,u as E,r as w,c as v,X as I,o as $,I as B,a as u,b as l,e as t,f as g,g as h,i as r,t as d,j as D,F,q as N,n as S,l as m,Y as U}from"./index-CoxzeSM8.js";import{I as i,_ as V}from"./_plugin-vue_export-helper-Pwn8nLHl.js";const M={class:"language-switcher"},O=["title"],j={class:"current-lang"},q={class:"dropdown-header"},x={class:"language-options"},z=["disabled","onClick"],K={class:"lang-code"},X={class:"lang-name"},Y=C({__name:"LanguageSwitcher",setup(A){const{locale:_}=E(),a=w(!1),o=v(()=>_.value),k=v(()=>I.opts),b=v(()=>{const e=k.value.find(n=>n.value===o.value);return e?e.title:"Unknown"}),L=()=>{a.value=!a.value},c=w(!1),y=async e=>{if(!(c.value||o.value===e))try{c.value=!0,await U(e),a.value=!1}catch(n){console.error("Failed to change language:",n),a.value=!1}finally{c.value=!1}},f=e=>{e.target.closest(".language-switcher")||(a.value=!1)},p=e=>{e.key==="Escape"&&(a.value=!1)};return $(()=>{document.addEventListener("click",f),document.addEventListener("keydown",p)}),B(()=>{document.removeEventListener("click",f),document.removeEventListener("keydown",p)}),(e,n)=>(l(),u("div",M,[t("button",{class:"language-btn",onClick:L,title:e.$t("language.switch")},[h(r(i),{icon:"carbon:translate",width:"18"}),t("span",j,d(b.value),1),h(r(i),{icon:a.value?"carbon:chevron-up":"carbon:chevron-down",width:"14",class:"chevron"},null,8,["icon"])],8,O),a.value?(l(),u("div",{key:0,class:"language-dropdown",onClick:n[1]||(n[1]=D(()=>{},["stop"]))},[t("div",q,[t("span",null,d(e.$t("language.switch")),1),t("button",{class:"close-btn",onClick:n[0]||(n[0]=s=>a.value=!1)},[h(r(i),{icon:"carbon:close",width:"16"})])]),t("div",x,[(l(!0),u(F,null,N(k.value,s=>(l(),u("button",{key:s.value,class:S(["language-option",{active:o.value===s.value,loading:c.value&&o.value!==s.value}]),disabled:c.value,onClick:G=>y(s.value)},[t("span",K,d(s.value.toUpperCase()),1),t("span",X,d(s.title),1),c.value&&o.value!==s.value?(l(),m(r(i),{key:0,icon:"carbon:circle-dash",width:"16",class:"loading-icon"})):o.value===s.value?(l(),m(r(i),{key:1,icon:"carbon:checkmark",width:"16",class:"check-icon"})):g("",!0)],10,z))),128))])])):g("",!0),a.value?(l(),u("div",{key:1,class:"backdrop",onClick:n[2]||(n[2]=s=>a.value=!1)})):g("",!0)]))}}),P=V(Y,[["__scopeId","data-v-e19a44ca"]]);export{P as L};
