import{d as q,a as h,b as m,y as L,l as M,f as I,i as N,e as a,t as c,r as H,u as R,c as k,o as V,g as _,w as j,m as F,F as K,q as z,x as A,z as D}from"./index-CoxzeSM8.js";import{_ as U}from"./Java-AI-BYpq8IxI.js";import{I as P,_ as B}from"./_plugin-vue_export-helper-Pwn8nLHl.js";import{L as G}from"./LanguageSwitcher-XuP7-_TV.js";import{u as Q,s as l}from"./sidebar-B6YvzJr4.js";import"./llm-check-BVkAKrj3.js";const W={key:1,class:"blur-card-content"},X=q({__name:"BlurCard",props:{content:{},wrapperStyle:{}},emits:["clickCard"],setup(T,{emit:p}){const n=T,r=p,i=()=>{console.log("[BlurCard] handleClick called with content:",n.content),r("clickCard",n.content),console.log("[BlurCard] clickCard event emitted")};return(e,w)=>{var d,g,v,b,y;return m(),h("button",{class:"blur-card",onClick:i,style:L(e.wrapperStyle)},[(d=e.content)!=null&&d.icon?(m(),M(N(P),{key:0,icon:e.content.icon,class:"blur-card-icon"},null,8,["icon"])):I("",!0),(g=e.content)!=null&&g.title||(v=e.content)!=null&&v.description?(m(),h("div",W,[a("h3",null,c((b=e.content)==null?void 0:b.title),1),a("p",null,c((y=e.content)==null?void 0:y.description),1)])):I("",!0)],4)}}}),Y=B(X,[["__scopeId","data-v-45044436"]]),Z={class:"home-page"},ee={class:"welcome-container"},te={class:"header"},oe={class:"header-top"},ae={class:"logo-container"},se={class:"tagline"},ne={class:"main-content"},le={class:"conversation-container"},re={class:"welcome-section"},ie={class:"welcome-title"},ce={class:"welcome-subtitle"},pe={class:"input-section"},me={class:"input-container"},de=["placeholder"],ue=["disabled"],he={class:"examples-section"},ge={class:"examples-grid"},ve={class:"card-type"},be=q({__name:"index",setup(T){const p=A(),n=Q(),r=H(""),i=H(),{t:e}=R(),w=()=>{const o=Date.now().toString();p.push({name:"direct",params:{id:o}}).then(()=>{console.log("[Home] jump to direct page"+e("common.success"))}).catch(t=>{console.error("[Home] jump to direct page"+e("common.error"),t)})},d=k(()=>[{title:e("home.examples.stockPrice.title"),type:"message",description:e("home.examples.stockPrice.description"),icon:"carbon:chart-line-data",prompt:e("home.examples.stockPrice.prompt")},{title:e("home.examples.weather.title"),type:"message",description:e("home.examples.weather.description"),icon:"carbon:partly-cloudy",prompt:e("home.examples.weather.prompt")}]),g=k(()=>[{title:e("home.examples.queryplan.title"),type:"plan-act",description:e("home.examples.queryplan.description"),icon:"carbon:plan",prompt:e("home.examples.queryplan.prompt"),planJson:{planType:"simple",title:e("home.examples.queryplan.planTitle"),steps:[{stepRequirement:e("home.examples.queryplan.step1"),terminateColumns:e("home.examples.queryplan.step1Output")},{stepRequirement:e("home.examples.queryplan.step2"),terminateColumns:e("home.examples.queryplan.step2Output")}],planTemplateId:"planTemplate-1749200517403"}},{title:e("home.examples.ainovel.title"),type:"plan-act",description:e("home.examples.ainovel.description"),icon:"carbon:document-tasks",prompt:e("home.examples.ainovel.prompt"),planJson:{planType:"simple",title:e("home.examples.ainovel.planTitle"),steps:[{stepRequirement:e("home.examples.ainovel.step1"),terminateColumns:e("home.examples.ainovel.step1Output")},{stepRequirement:e("home.examples.ainovel.step2"),terminateColumns:e("home.examples.ainovel.step2Output")}],planTemplateId:"planTemplate-1753622676988"}},{title:e("home.examples.formInputDemo.title"),type:"plan-act",description:e("home.examples.formInputDemo.description"),icon:"carbon:watson",prompt:e("home.examples.formInputDemo.prompt"),planJson:{planType:"simple",title:e("home.examples.formInputDemo.planTitle"),steps:[{stepRequirement:e("home.examples.formInputDemo.step1"),terminateColumns:e("home.examples.formInputDemo.step1Output")},{stepRequirement:e("home.examples.formInputDemo.step2"),terminateColumns:e("home.examples.formInputDemo.step2Output")},{stepRequirement:e("home.examples.formInputDemo.step3"),terminateColumns:e("home.examples.formInputDemo.step3Output")}],planTemplateId:"planTemplate-forminput-demo-2025"}}]),v=k(()=>[...d.value,...g.value]),b=o=>{o.type==="message"?O(o):o.type==="plan-act"&&$(o)};V(()=>{console.log("[Home] onMounted called"),console.log("[Home] taskStore:",n),console.log("[Home] examples:",d),n.markHomeVisited(),console.log("[Home] Home visited marked")});const y=async o=>{try{l.createNewTemplate(o.planType),l.jsonContent=JSON.stringify(o);const t=await l.saveTemplate();return t!=null&&t.duplicate?console.log("[Sidebar] "+e("sidebar.saveCompleted",{message:t.message,versionCount:t.versionCount})):t!=null&&t.saved?console.log("[Sidebar] "+e("sidebar.saveSuccess",{message:t.message,versionCount:t.versionCount})):t!=null&&t.message&&console.log("[Sidebar] "+e("sidebar.saveStatus",{message:t.message})),t}catch(t){throw console.error("[Sidebar] Failed to save the plan to the template library:",t),alert(t.message||e("sidebar.saveFailed")),t}},J=()=>{D(()=>{i.value&&(i.value.style.height="auto",i.value.style.height=Math.min(i.value.scrollHeight,200)+"px")})},E=o=>{console.log("[Home] handleKeydown called, key:",o.key),o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),console.log("[Home] Enter key pressed, calling handleSend"),S())},S=()=>{if(console.log("[Home] handleSend called, userInput:",r.value),!r.value.trim()){console.log("[Home] handleSend aborted - empty input");return}const o=r.value.trim();console.log("[Home] Setting task to store:",o),n.setTask(o),console.log("[Home] Task set to store, current task:",n.currentTask);const t=Date.now().toString();console.log("[Home] Navigating to direct page with chatId:",t),p.push({name:"direct",params:{id:t}}).then(()=>{console.log("[Home] Navigation to direct page completed")}).catch(s=>{console.error("[Home] Navigation error:",s)})},O=o=>{console.log("[Home] selectExample called with example:",o),console.log("[Home] Example prompt:",o.prompt),n.setTask(o.prompt),console.log("[Home] Task set to store from example, current task:",n.currentTask);const t=Date.now().toString();console.log("[Home] Navigating to direct page with chatId:",t),p.push({name:"direct",params:{id:t}}).then(()=>{console.log("[Home] Navigation to direct page completed (from example)")}).catch(s=>{console.error("[Home] Navigation error (from example):",s)})},$=async o=>{console.log("[Home] selectPlan called with plan:",o);try{const t=await y(o.planJson);console.log("[Home] Plan saved to templates, saveResult:",t);const s=Date.now().toString();await p.push({name:"direct",params:{id:s}}),D(async()=>{await new Promise(u=>setTimeout(u,300)),l.isCollapsed?(await l.toggleSidebar(),console.log("[Sidebar] Sidebar toggled")):console.log("[Sidebar] Sidebar is already open"),await l.loadPlanTemplateList(),console.log("[Sidebar] Template list loaded");const f=(t==null?void 0:t.planId)||o.planJson.planTemplateId,x=l.planTemplateList.find(u=>u.id===f);if(!x){console.error("[Sidebar] Template not found for ID:",f),console.log("[Sidebar] Available templates:",l.planTemplateList.map(u=>u.id));return}await l.selectTemplate(x),console.log("[Sidebar] Template selected:",x.title);const C=document.querySelector(".execute-btn");C.disabled?console.error("[Sidebar] Execute button not found or disabled"):(console.log("[Sidebar] Triggering execute button click"),C.click())})}catch(t){console.error("[Home] Error in selectPlan:",t)}};return(o,t)=>(m(),h("div",Z,[a("div",ee,[t[2]||(t[2]=a("div",{class:"background-effects"},[a("div",{class:"gradient-orb orb-1"}),a("div",{class:"gradient-orb orb-2"}),a("div",{class:"gradient-orb orb-3"})],-1)),a("header",te,[a("div",oe,[_(G)]),a("div",ae,[t[1]||(t[1]=a("div",{class:"logo"},[a("img",{src:U,alt:"JManus",class:"java-logo"}),a("h1",null,"JManus")],-1)),a("span",se,c(o.$t("home.tagline")),1)])]),a("main",ne,[a("div",le,[a("div",re,[a("h2",ie,c(o.$t("home.welcomeTitle")),1),a("p",ce,c(o.$t("home.welcomeSubtitle")),1),a("button",{class:"direct-button",onClick:w},c(o.$t("home.directButton")),1)]),a("div",pe,[a("div",me,[j(a("textarea",{"onUpdate:modelValue":t[0]||(t[0]=s=>r.value=s),ref_key:"textareaRef",ref:i,class:"main-input",placeholder:o.$t("home.inputPlaceholder"),onKeydown:E,onInput:J},null,40,de),[[F,r.value]]),a("button",{class:"send-button",disabled:!r.value.trim(),onClick:S},[_(N(P),{icon:"carbon:send-alt"})],8,ue)])]),a("div",he,[a("div",ge,[(m(!0),h(K,null,z(v.value,s=>(m(),h("div",{key:s.title,class:"card-with-type"},[_(Y,{content:s,onClickCard:f=>b(s)},null,8,["content","onClickCard"]),a("span",ve,c(s.type),1)]))),128))])])])])])]))}}),we=B(be,[["__scopeId","data-v-15ee0051"]]);export{we as default};
