server:
  port: 18080
spring:
  config:
    import: classpath:application-database-tool.yml
  application:
    name: spring-ai-alibaba-openmanus
  profiles:
    active: h2
  main:
    allow-circular-references: true
    lazy-initialization: false
  aop:
    proxy-target-class: true
  servlet:
    multipart:
      max-file-size: 5368709120
      max-request-size: 6442450944
      enabled: true
      file-size-threshold: 2048
  # Hikari Configuration
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: Spring-AI-Alibaba-JManus-${spring.profiles.active}-Pool
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
  # JPA Configuration
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  # Spring AI 配置
  ai:
    # 禁用 Spring AI 的自动配置，避免 ChatModel 相关错误
    autoconfigure:
      exclude:
        - org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration

# 日志配置
logging:
  file:
    name: ./logs/info.log
  level:
    root: INFO
    com.alibaba.cloud.ai.example.manus.inhouse.registry.McpToolRegistry: INFO
    com.alibaba.cloud.ai.example.manus.tool: DEBUG
    com.alibaba.cloud.ai.example.manus.inhouse.tool: DEBUG

# 计划轮询配置
manus:
  plan:
    polling:
      # 是否启用轮询
      enable-polling: true
      # 最大轮询次数
      max-attempts: 60
      # 轮询间隔（毫秒）
      poll-interval: 2000
      # 连接超时时间（毫秒）
      connect-timeout: 5000
      # 读取超时时间（毫秒）
      read-timeout: 10000
      # 指数退避基础延迟（毫秒）
      base-backoff-delay: 1000
      # 最大退避延迟（毫秒）
      max-backoff-delay: 10000

namespace:
  value: default

agent:
  # Initialize save agent on startup
  init: true
  # Serialize: fastjson | jackson
  serialize: jackson
