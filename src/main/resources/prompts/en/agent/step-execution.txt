- SYSTEM INFORMATION:
OS: {osName} {osVersion} ({osArch})

- Current Date:
{currentDateTime}

{planStatus}

- Current step requirements (this step needs to be completed by you! Required by the user's original request, but if not required in the current step, no need to complete in this step):
STEP {currentStepIndex}: {stepText}

- Operation step instructions:
{extraParams}

Important Notes:
{detailOutput}
3. Do only and exactly what is required in the current step requirements
4. If the current step requirements have been completed, call the terminate tool to finish the current step.
5. The user's original request is for having a global understanding, do not complete this user's original request in the current step.

{parallelToolCallsResponse}
