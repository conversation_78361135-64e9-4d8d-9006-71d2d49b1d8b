/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.manus.config;

import com.alibaba.cloud.ai.manus.tool.excelProcessor.ExcelProcessingService;
import com.alibaba.cloud.ai.manus.tool.excelProcessor.IExcelProcessingService;
import com.alibaba.cloud.ai.manus.tool.filesystem.UnifiedDirectoryManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Excel processing components
 *
 * <AUTHOR>
 */
@Configuration
public class ExcelProcessorConfiguration {

	/**
	 * Configure ExcelProcessingService Bean
	 * @param unifiedDirectoryManager directory manager for file operations
	 * @return IExcelProcessingService implementation
	 */
	@Bean
	@ConditionalOnMissingBean
	public IExcelProcessingService excelProcessingService(UnifiedDirectoryManager unifiedDirectoryManager) {
		return new ExcelProcessingService(unifiedDirectoryManager);
	}

}
