var g=Object.defineProperty;var f=(n,e,t)=>e in n?g(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var a=(n,e,t)=>f(n,typeof e!="symbol"?e+"":e,t);import{P as w,r as d,A as y,Q as h}from"./index-CoxzeSM8.js";import{L as u}from"./llm-check-BVkAKrj3.js";const A=w("task",()=>{const n=d(null),e=d(""),t=d(!1);return{currentTask:n,taskToInput:e,hasVisitedHome:t,setTask:i=>{console.log("[TaskStore] setTask called with prompt:",i);const T={prompt:i,timestamp:Date.now(),processed:!1};n.value=T,console.log("[TaskStore] Task set, currentTask.value:",n.value)},setTaskToInput:i=>{console.log("[TaskStore] setTaskToInput called with prompt:",i),e.value=i,console.log("[TaskStore] Task to input set:",e.value)},getAndClearTaskToInput:()=>{const i=e.value;return e.value="",console.log("[TaskStore] getAndClearTaskToInput returning:",i),i},markTaskAsProcessed:()=>{console.log("[TaskStore] markTaskAsProcessed called, current task:",n.value),n.value?(n.value.processed=!0,console.log("[TaskStore] Task marked as processed:",n.value)):console.log("[TaskStore] No current task to mark as processed")},clearTask:()=>{n.value=null},hasUnprocessedTask:()=>{const i=n.value&&!n.value.processed;return console.log("[TaskStore] hasUnprocessedTask check - currentTask:",n.value,"result:",i),i},markHomeVisited:()=>{t.value=!0,localStorage.setItem("hasVisitedHome","true")},checkHomeVisited:()=>{const i=localStorage.getItem("hasVisitedHome");return t.value=i==="true",t.value},resetHomeVisited:()=>{t.value=!1,localStorage.removeItem("hasVisitedHome")},emitPlanExecutionRequested:i=>{console.log("[TaskStore] emitPlanExecutionRequested called with payload:",i),window.dispatchEvent(new CustomEvent("plan-execution-requested",{detail:i}))}}});class p{static async generatePlan(e,t,s="simple"){return u.withLlmCheck(async()=>{const o={query:e,planType:s};t&&(o.existingJson=t);const l=await fetch(`${this.PLAN_TEMPLATE_URL}/generate`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!l.ok)throw new Error(`Failed to generate plan: ${l.status}`);const r=await l.json();if(r.planJson)try{r.plan=JSON.parse(r.planJson)}catch{r.plan={error:"Unable to parse plan data"}}return r})}static async executePlan(e,t,s,o){return u.withLlmCheck(async()=>{console.log("[PlanActApiService] executePlan called with:",{planTemplateId:e,rawParam:t,uploadedFiles:s,replacementParams:o});const l={toolName:e};t&&(l.rawParam=t),s&&s.length>0&&(l.uploadedFiles=s,console.log("[PlanActApiService] Including uploaded files:",s.length)),o&&Object.keys(o).length>0&&(l.replacementParams=o,console.log("[PlanActApiService] Including replacement params:",o)),console.log("[PlanActApiService] Making request to:","/api/executor/executeByToolNameAsync"),console.log("[PlanActApiService] Request body:",l);const r=await fetch("/api/executor/executeByToolNameAsync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(console.log("[PlanActApiService] Response status:",r.status,r.ok),!r.ok){const m=await r.text();throw console.error("[PlanActApiService] Request failed:",m),new Error(`Failed to execute plan: ${r.status}`)}const c=await r.json();return console.log("[PlanActApiService] executePlan response:",c),c})}static async savePlanTemplate(e,t){const s=await fetch(`${this.PLAN_TEMPLATE_URL}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e,planJson:t})});if(!s.ok)throw new Error(`Failed to save plan: ${s.status}`);return await s.json()}static async getPlanVersions(e){const t=await fetch(`${this.PLAN_TEMPLATE_URL}/versions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e})});if(!t.ok)throw new Error(`Failed to get plan versions: ${t.status}`);return await t.json()}static async getVersionPlan(e,t){const s=await fetch(`${this.PLAN_TEMPLATE_URL}/get-version`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e,versionIndex:t.toString()})});if(!s.ok)throw new Error(`Failed to get specific version plan: ${s.status}`);return await s.json()}static async getAllPlanTemplates(){const e=await fetch(`${this.PLAN_TEMPLATE_URL}/list`);if(!e.ok)throw new Error(`Failed to get plan template list: ${e.status}`);return await e.json()}static async updatePlanTemplate(e,t,s,o="simple"){return u.withLlmCheck(async()=>{const l={planId:e,query:t,planType:o};s&&(l.existingJson=s);const r=await fetch(`${this.PLAN_TEMPLATE_URL}/update`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!r.ok)throw new Error(`Failed to update plan template: ${r.status}`);const c=await r.json();if(c.planJson)try{c.plan=JSON.parse(c.planJson)}catch{c.plan={error:"Unable to parse plan data"}}return c})}static async deletePlanTemplate(e){const t=await fetch(`${this.PLAN_TEMPLATE_URL}/delete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e})});if(!t.ok)throw new Error(`Failed to delete plan template: ${t.status}`);return await t.json()}static async createCronTask(e){const t=await fetch(this.CRON_TASK_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)try{const s=await t.json();throw new Error(s.message||`Failed to create cron task: ${t.status}`)}catch{throw new Error(`Failed to create cron task: ${t.status}`)}return await t.json()}}a(p,"PLAN_TEMPLATE_URL","/api/plan-template"),a(p,"CRON_TASK_URL","/api/cron-tasks");class S{constructor(){a(this,"isCollapsed",!0);a(this,"currentTab","list");a(this,"currentPlanTemplateId",null);a(this,"planTemplateList",[]);a(this,"selectedTemplate",null);a(this,"isLoading",!1);a(this,"errorMessage","");a(this,"jsonContent","");a(this,"planType","dynamic_agent");a(this,"generatorPrompt","");a(this,"executionParams","");a(this,"isGenerating",!1);a(this,"isExecuting",!1);a(this,"planVersions",[]);a(this,"currentVersionIndex",-1);a(this,"availableTools",[]);a(this,"isLoadingTools",!1);a(this,"toolsLoadError","");this.planVersions=[],this.currentVersionIndex=-1}parseDateTime(e){return e?Array.isArray(e)&&e.length>=6?new Date(e[0],e[1]-1,e[2],e[3],e[4],e[5],Math.floor(e[6]/1e6)):typeof e=="string"?new Date(e):new Date:new Date}get sortedTemplates(){return[...this.planTemplateList].sort((e,t)=>{const s=this.parseDateTime(e.updateTime??e.createTime);return this.parseDateTime(t.updateTime??t.createTime).getTime()-s.getTime()})}get canRollback(){return this.planVersions&&this.planVersions.length>1&&this.currentVersionIndex>0}get canRestore(){return this.planVersions&&this.planVersions.length>1&&this.currentVersionIndex<this.planVersions.length-1}get computedApiUrl(){if(!this.selectedTemplate)return"";const e=`/api/plan-template/execute/${this.selectedTemplate.id}`,t=this.executionParams.trim();return t?`${e}?allParams=${encodeURIComponent(t)}`:e}toggleSidebar(){this.isCollapsed=!this.isCollapsed}switchToTab(e){this.currentTab=e}async loadPlanTemplateList(){this.isLoading=!0,this.errorMessage="";try{console.log("[SidebarStore] Starting to load plan template list...");const e=await p.getAllPlanTemplates();e!=null&&e.templates&&Array.isArray(e.templates)?(this.planTemplateList=e.templates,console.log(`[SidebarStore] Successfully loaded ${e.templates.length} plan templates`)):(this.planTemplateList=[],console.warn("[SidebarStore] API returned abnormal data format, using empty list",e))}catch(e){console.error("[SidebarStore] Failed to load plan template list:",e),this.planTemplateList=[],this.errorMessage=`Load failed: ${e.message}`}finally{this.isLoading=!1}}async selectTemplate(e){this.currentPlanTemplateId=e.id,this.selectedTemplate=e,this.currentTab="config",await this.loadTemplateData(e),console.log(`[SidebarStore] Selected plan template: ${e.id}`)}async loadTemplateData(e){try{const t=await p.getPlanVersions(e.id);if(this.planVersions=t.versions||[],this.planVersions.length>0){const s=this.planVersions[this.planVersions.length-1];this.jsonContent=s,this.currentVersionIndex=this.planVersions.length-1;try{const o=JSON.parse(s);o.prompt&&(this.generatorPrompt=o.prompt),o.params&&(this.executionParams=o.params),o.planType&&(this.planType=o.planType,console.log(`[SidebarStore] Updated planType to: ${this.planType}`))}catch{console.warn("Unable to parse JSON content to get prompt information")}}else this.jsonContent="",this.generatorPrompt="",this.executionParams="",this.planType="dynamic_agent"}catch(t){throw console.error("Failed to load template data:",t),t}}createNewTemplate(e){const t={id:`new-${Date.now()}`,title:h.global.t("sidebar.newTemplateName"),description:h.global.t("sidebar.newTemplateDescription"),createTime:new Date().toISOString(),updateTime:new Date().toISOString()};this.selectedTemplate=t,this.currentPlanTemplateId=null,this.jsonContent="",this.generatorPrompt="",this.executionParams="",this.planVersions=[],this.currentVersionIndex=-1,this.currentTab="config",this.planType=e,console.log("[SidebarStore] Created new empty plan template, switching to config tab")}async deleteTemplate(e){if(!e.id){console.warn("[SidebarStore] deleteTemplate: Invalid template object or ID");return}try{await p.deletePlanTemplate(e.id),this.currentPlanTemplateId===e.id&&this.clearSelection(),await this.loadPlanTemplateList(),console.log(`[SidebarStore] Plan template ${e.id} has been deleted`)}catch(t){throw console.error("Failed to delete plan template:",t),await this.loadPlanTemplateList(),t}}clearSelection(){this.currentPlanTemplateId=null,this.selectedTemplate=null,this.jsonContent="",this.generatorPrompt="",this.executionParams="",this.planVersions=[],this.currentVersionIndex=-1,this.currentTab="list"}clearExecutionParams(){this.executionParams=""}rollbackVersion(){this.canRollback&&this.planVersions&&this.currentVersionIndex>0&&(this.currentVersionIndex--,this.jsonContent=this.planVersions[this.currentVersionIndex]||"")}restoreVersion(){this.canRestore&&this.planVersions&&this.currentVersionIndex<this.planVersions.length-1&&(this.currentVersionIndex++,this.jsonContent=this.planVersions[this.currentVersionIndex]||"")}async saveTemplate(){if(!this.selectedTemplate)return;const e=this.jsonContent.trim();if(!e)throw new Error("Content cannot be empty");try{JSON.parse(e)}catch(t){throw new Error(`Invalid format, please correct and save.
Error: `+t.message)}try{const t=await p.savePlanTemplate(this.selectedTemplate.id,e);return t!=null&&t.planId&&this.selectedTemplate.id.startsWith("new-")&&(console.log("[SidebarStore] Updating template ID from",this.selectedTemplate.id,"to",t.planId),this.selectedTemplate.id=t.planId,this.currentPlanTemplateId=t.planId),this.currentVersionIndex<this.planVersions.length-1&&(this.planVersions=this.planVersions.slice(0,this.currentVersionIndex+1)),this.planVersions.push(e),this.currentVersionIndex=this.planVersions.length-1,t}catch(t){throw console.error("Failed to save plan template:",t),t}}async generatePlan(){if(this.generatorPrompt.trim()){this.isGenerating=!0;try{const e=await p.generatePlan(this.generatorPrompt,void 0,this.planType);if(this.jsonContent=e.planJson||"",this.selectedTemplate&&this.selectedTemplate.id.startsWith("new-")){let t="New Plan Template";try{t=JSON.parse(e.planJson||"{}").title||t}catch{console.warn("Unable to parse plan JSON to get title")}this.selectedTemplate={id:e.planTemplateId,title:t,description:h.global.t("sidebar.generatedTemplateDescription"),createTime:new Date().toISOString(),updateTime:new Date().toISOString(),planJson:e.planJson},this.currentPlanTemplateId=e.planTemplateId,await this.loadPlanTemplateList()}return this.currentVersionIndex<this.planVersions.length-1&&(this.planVersions=this.planVersions.slice(0,this.currentVersionIndex+1)),this.planVersions.push(this.jsonContent),this.currentVersionIndex=this.planVersions.length-1,e}catch(e){throw console.error("Failed to generate plan:",e),e}finally{this.isGenerating=!1}}}async updatePlan(){if(!(!this.generatorPrompt.trim()||!this.jsonContent.trim())&&this.selectedTemplate){this.isGenerating=!0;try{const e=await p.updatePlanTemplate(this.selectedTemplate.id,this.generatorPrompt,this.jsonContent,this.planType);return this.jsonContent=e.planJson||"",this.currentVersionIndex<this.planVersions.length-1&&(this.planVersions=this.planVersions.slice(0,this.currentVersionIndex+1)),this.planVersions.push(this.jsonContent),this.currentVersionIndex=this.planVersions.length-1,e}catch(e){throw console.error("Failed to update plan:",e),e}finally{this.isGenerating=!1}}}preparePlanExecution(){if(!this.selectedTemplate)return null;this.isExecuting=!0;try{let e;try{e=JSON.parse(this.jsonContent),e.planTemplateId=this.selectedTemplate.id}catch{e={planTemplateId:this.selectedTemplate.id,title:this.selectedTemplate.title??h.global.t("sidebar.defaultExecutionPlanTitle"),steps:[{stepRequirement:"[BROWSER_AGENT] Visit Baidu to search for Alibaba's latest stock price"},{stepRequirement:"[DEFAULT_AGENT] Extract and organize stock price information from search results"},{stepRequirement:"[TEXT_FILE_AGENT] Create a text file to record query results"},{stepRequirement:"[DEFAULT_AGENT] Report query results to user"}]}}return{title:this.selectedTemplate.title??e.title??"Execution Plan",planData:e,params:this.executionParams.trim()||void 0,replacementParams:void 0}}catch(e){throw console.error("Failed to prepare plan execution:",e),this.isExecuting=!1,e}}finishPlanExecution(){this.isExecuting=!1}async loadAvailableTools(){if(!this.isLoadingTools){this.isLoadingTools=!0,this.toolsLoadError="";try{console.log("[SidebarStore] Loading available tools...");const e=await fetch("/api/agents/tools");if(e.ok){const t=await e.json();console.log("[SidebarStore] Loaded available tools:",t),this.availableTools=t}else console.error("[SidebarStore] Failed to load tools:",e.statusText),this.toolsLoadError=`Failed to load tools: ${e.statusText}`,this.availableTools=[]}catch(e){console.error("[SidebarStore] Error loading tools:",e),this.toolsLoadError=e instanceof Error?e.message:"Unknown error",this.availableTools=[]}finally{this.isLoadingTools=!1}}}}const L=y(new S);export{p as P,L as s,A as u};
