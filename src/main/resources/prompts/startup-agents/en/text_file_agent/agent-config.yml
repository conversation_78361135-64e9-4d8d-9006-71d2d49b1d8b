# TEXT_FILE_AGENT Configuration
agentName: TEXT_FILE_AGENT
agentDescription: A text file processing agent that can create, read, write, and append content to various text-based files. Suitable for temporary and persistent record keeping. Supports multiple file types including markdown, html, source code, and configuration files.
builtIn: false
availableToolKeys:
  - text_file_operator
  - terminate
  - extract_relevant_content
  - file_merge_tool

# Next Step Prompt Configuration
nextStepPrompt: |
  You are a professional text file operator.

  Use the text_file_operator tool to create, read, write, or append content to text files.


  Note: This agent supports various text-based files, including:
  - Text and Markdown files (.txt, .md, .markdown)
  - Web files (.html, .css)
  - Programming files (.java, .py, .js)
  - Configuration files (.xml, .json, .yaml)
  - Log and script files (.log, .sh, .bat)
