PLANNING_PLAN_CREATION=构建执行计划的Prompt，如果分解任务做的不好，调这个
AGENT_CURRENT_STEP_ENV=用来定义当前的环境信息，对应agent从过去调用的所有函数的结果，也就是当前的环境信息，因为要存到agent里面所以单独有一个项
AGENT_STEP_EXECUTION=每个agent执行步骤时候都会给agent的上下文信息，大部分的变量不要调（因为都是预制的），可以调整一些对他的建议，一个重点的agent步骤执行prompt
PLANNING_PLAN_FINALIZER=用来做最终总结的prompt，对应任务结束以后告知用户的那个动作，已合并用户请求信息
DIRECT_RESPONSE=用于直接反馈模式的prompt，当用户请求无需复杂规划时直接返回结果
AGENT_STUCK_ERROR=Agent执行卡住时的错误提示信息
SUMMARY_PLAN_TEMPLATE=内容总结执行计划的JSON模板
MAPREDUCE_TOOL_DESCRIPTION=MapReduce计划工具的描述信息
MAPREDUCE_TOOL_PARAMETERS=MapReduce计划工具的参数定义JSON
AGENT_DEBUG_DETAIL_OUTPUT=Agent调试模式下的详细输出要求
AGENT_NORMAL_OUTPUT=Agent正常模式下的输出要求
AGENT_PARALLEL_TOOL_CALLS_RESPONSE=Agent并行工具调用的响应规则
FORM_INPUT_TOOL_DESCRIPTION=表单输入工具的描述信息
FORM_INPUT_TOOL_PARAMETERS=表单输入工具的参数定义JSON

# 工具描述
BASH_TOOL_DESCRIPTION=Bash命令执行工具的描述信息
BASH_TOOL_PARAMETERS=Bash命令执行工具的参数定义JSON
TEXTFILEOPERATOR_TOOL_DESCRIPTION=文本文件操作工具的描述信息
TEXTFILEOPERATOR_TOOL_PARAMETERS=文本文件操作工具的参数定义JSON
BROWSER_USE_TOOL_DESCRIPTION=浏览器自动化工具的描述信息
BROWSER_USE_TOOL_PARAMETERS=浏览器自动化工具的参数定义JSON
PYTHON_EXECUTE_TOOL_DESCRIPTION=Python代码执行工具的描述信息
PYTHON_EXECUTE_TOOL_PARAMETERS=Python代码执行工具的参数定义JSON
DATABASE_USE_TOOL_DESCRIPTION=数据库操作工具的描述信息
DATABASE_USE_TOOL_PARAMETERS=数据库操作工具的参数定义JSON
CRON_TOOL_TOOL_DESCRIPTION=定时任务工具的描述信息
CRON_TOOL_TOOL_PARAMETERS=定时任务工具的参数定义JSON
DOC_LOADER_TOOL_DESCRIPTION=文档加载工具的描述信息
DOC_LOADER_TOOL_PARAMETERS=文档加载工具的参数定义JSON
FILE_MERGE_TOOL_TOOL_DESCRIPTION=文件合并工具的描述信息
FILE_MERGE_TOOL_TOOL_PARAMETERS=文件合并工具的参数定义JSON
DATA_SPLIT_TOOL_DESCRIPTION=数据分割工具的描述信息
DATA_SPLIT_TOOL_PARAMETERS=数据分割工具的参数定义JSON
MAP_OUTPUT_TOOL_DESCRIPTION=映射输出工具的描述信息
MAP_OUTPUT_TOOL_PARAMETERS=映射输出工具的参数定义JSON
REDUCE_OPERATION_TOOL_DESCRIPTION=归约操作工具的描述信息
REDUCE_OPERATION_TOOL_PARAMETERS=归约操作工具的参数定义JSON
FINALIZE_TOOL_DESCRIPTION=完成工具的描述信息
FINALIZE_TOOL_PARAMETERS=完成工具的参数定义JSON
TERMINATE_TOOL_DESCRIPTION=终止工具的描述信息
TERMINATE_TOOL_PARAMETERS=终止工具的参数定义JSON
PPTGENERATOROPERATOR_TOOL_DESCRIPTION=PPT生成器工具的描述信息
PPTGENERATOROPERATOR_TOOL_PARAMETERS=PPT生成器工具的参数定义JSON
