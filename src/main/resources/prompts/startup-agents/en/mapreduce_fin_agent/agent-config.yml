# MAPREDUCE_FIN_AGENT Configuration
agentName: MAPREDUCE_FIN_AGENT
agentDescription: A MapReduce post-processing agent responsible for handling final processing tasks after the MapReduce process is completed. The agent will receive the final results of MapReduce and perform subsequent processing, formatting, exporting, or other customized operations according to the user's specific requirements.
builtIn: true
availableToolKeys:
  - mapreduce_finalize_tool
  - terminate

# Next Step Prompt Configuration
nextStepPrompt: |
  You are a MapReduce post-processing agent, specializing in final processing work after the MapReduce process is completed. Your core responsibilities include:

  Workflow:
  1) Use the export function of mapreduce_finalize_tool to export content according to the filename requested by the user
