/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.manus.config;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Configuration option annotation
 * <p>
 * Used to define options for dropdown boxes, radio buttons, etc.
 */
@Target({})
@Retention(RetentionPolicy.RUNTIME)

public @interface ConfigOption {

	/**
	 * Option value
	 */
	String value();

	/**
	 * Option label
	 * <p>
	 * Supports internationalization key format:
	 * config.option.{group}.{subGroup}.{key}.{value}
	 */
	String label() default "";

	/**
	 * Option description
	 * <p>
	 * Supports internationalization key format:
	 * config.option.desc.{group}.{subGroup}.{key}.{value}
	 */
	String description() default "";

	/**
	 * Option icon (optional)
	 */
	String icon() default "";

	/**
	 * Whether option is disabled
	 */
	boolean disabled() default false;

}
