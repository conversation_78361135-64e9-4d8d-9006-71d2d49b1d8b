var o=Object.defineProperty;var l=(e,t,i)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i;var n=(e,t,i)=>l(e,typeof t!="symbol"?t+"":t,i);class c{static async checkLlmConfiguration(){const t=Date.now();if(this.cachedStatus&&t-this.cachedStatus.lastCheck<this.CACHE_DURATION)return{initialized:this.cachedStatus.initialized};try{const i=await fetch("/api/init/status");if(!i.ok)throw new Error(`Check failed: ${i.status}`);const s=await i.json(),a=s.success&&s.initialized;return this.cachedStatus={initialized:a,lastCheck:t},a?{initialized:!0}:{initialized:!1,message:"System has not configured LLM model yet, please configure API key through initialization page first."}}catch(i){return console.error("[LlmCheckService] Failed to check LLM configuration:",i),{initialized:!1,message:"Unable to check LLM configuration status, please ensure system is running normally."}}}static async ensureLlmConfigured(t){const{showAlert:i=!0,redirectToInit:s=!0}=t||{},a=await this.checkLlmConfiguration();if(!a.initialized){const r=a.message||"Please configure LLM model first";throw i&&alert(r),s?(localStorage.removeItem("hasInitialized"),window.location.href="/ui/#/init",new Error("Redirecting to initialization page")):new Error(r)}}static clearCache(){this.cachedStatus=null}static async withLlmCheck(t,i){return await this.ensureLlmConfigured(i),t()}}n(c,"cachedStatus",null),n(c,"CACHE_DURATION",3e4);export{c as L};
