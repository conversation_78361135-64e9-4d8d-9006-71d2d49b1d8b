import{d as A,o as Z,I as ee,l as x,b as p,J as N,g as k,T as R,p as D,a as f,f as V,e as o,t as r,n as I,i as S,R as z,j as G,c as B,r as b,B as M,w as F,m as oe,E as te,F as L,q as U,z as se,h as le,W as ae,A as ne}from"./index-CoxzeSM8.js";import{I as w,_ as E}from"./_plugin-vue_export-helper-Pwn8nLHl.js";const ce={class:"modal-header"},ie={class:"header-content"},re=["title"],de={class:"modal-content"},ue={class:"modal-footer"},pe=A({__name:"index",props:{modelValue:{type:Boolean,required:!0},title:{type:String,default:""},statusIcon:{type:String,default:""},statusIconClass:{type:String,default:""},statusIconTitle:{type:String,default:""}},emits:["update:modelValue","confirm"],setup(n,{emit:v}){const d=n,h=v,m=i=>{i.target===i.currentTarget&&(i.stopPropagation(),i.preventDefault())},y=i=>{i.key==="Escape"&&d.modelValue&&h("update:modelValue",!1)};return Z(()=>{document.addEventListener("keydown",y)}),ee(()=>{document.removeEventListener("keydown",y)}),(i,s)=>(p(),x(N,{to:"body"},[k(R,{name:"modal"},{default:D(()=>[n.modelValue?(p(),f("div",{key:0,class:"modal-overlay",onClick:m},[o("div",{class:"modal-container",onClick:s[3]||(s[3]=G(()=>{},["stop"]))},[o("div",ce,[o("div",ie,[o("h3",null,r(n.title),1),n.statusIcon?(p(),f("div",{key:0,class:I(["status-icon",n.statusIconClass]),title:n.statusIconTitle},[k(S(w),{icon:n.statusIcon,width:"16"},null,8,["icon"])],10,re)):V("",!0)]),o("button",{class:"close-btn",onClick:s[0]||(s[0]=l=>i.$emit("update:modelValue",!1))},[k(S(w),{icon:"carbon:close"})])]),o("div",de,[z(i.$slots,"default",{},void 0,!0)]),o("div",ue,[z(i.$slots,"footer",{},()=>[o("button",{class:"cancel-btn",onClick:s[1]||(s[1]=l=>i.$emit("update:modelValue",!1))},r(i.$t("common.cancel")),1),o("button",{class:"confirm-btn",onClick:s[2]||(s[2]=l=>i.$emit("confirm"))},r(i.$t("common.confirm")),1)],!0)])])])):V("",!0)]),_:3})]))}}),me=E(pe,[["__scopeId","data-v-ece0ef1e"]]),ve={class:"tool-selection-content"},he={class:"tool-controls"},fe={class:"search-container"},ge=["placeholder"],ye={class:"sort-container"},ke={value:"group"},_e={value:"name"},be={value:"enabled"},Te={class:"tool-summary"},$e={class:"summary-text"},Ce={key:0,class:"tool-groups"},Se=["onClick"],we={class:"group-title-area"},Ve={class:"group-name"},Be={class:"group-count"},Ie={class:"group-enable-all"},Me=["checked","onChange","data-group"],Ge={class:"enable-label"},Ae={class:"tool-info"},Ee={class:"tool-selection-name"},Le={key:0,class:"tool-selection-desc"},Ue={class:"tool-actions"},xe=["checked","onChange"],De={key:1,class:"empty-state"},Pe=A({__name:"ToolSelectionModal",props:{modelValue:{type:Boolean},tools:{},selectedToolIds:{}},emits:["update:modelValue","confirm"],setup(n,{emit:v}){const d=n,h=v,m=B({get:()=>d.modelValue,set:e=>h("update:modelValue",e)}),y=b(""),i=b("group"),s=b(new Set),l=b([]),g=(e,t)=>{const a=document.querySelector(`input[data-group="${e}"]`);a&&(a.indeterminate=W(t))};M(()=>d.selectedToolIds,e=>{l.value=[...e]},{immediate:!0});const T=B(()=>{let e=d.tools.filter(t=>t.key);if(y.value){const t=y.value.toLowerCase();e=e.filter(a=>{var u;return a.name.toLowerCase().includes(t)||a.description.toLowerCase().includes(t)||(((u=a.serviceGroup)==null?void 0:u.toLowerCase().includes(t))??!1)})}switch(i.value){case"name":e=[...e].sort((t,a)=>t.name.localeCompare(a.name));break;case"enabled":e=[...e].sort((t,a)=>{const u=l.value.includes(t.key),c=l.value.includes(a.key);return u&&!c?-1:!u&&c?1:t.name.localeCompare(a.name)});break;case"group":default:e=[...e].sort((t,a)=>{const u=t.serviceGroup??"Ungrouped",c=a.serviceGroup??"Ungrouped";return u!==c?u.localeCompare(c):t.name.localeCompare(a.name)});break}return e}),$=B(()=>{const e=new Map;return T.value.forEach(t=>{const a=t.serviceGroup??"Ungrouped";e.has(a)||e.set(a,[]),e.get(a).push(t)}),new Map([...e.entries()].sort())}),j=B(()=>T.value.length);M([l,$],()=>{se(()=>{for(const[e,t]of $.value)g(e,t)})},{flush:"post",deep:!1});const J=e=>l.value.includes(e),O=(e,t)=>{t.stopPropagation();const u=t.target.checked;if(!e){console.error("toolKey is undefined, cannot proceed");return}u?l.value.includes(e)||(l.value=[...l.value,e]):l.value=l.value.filter(c=>c!==e)},P=e=>e.filter(t=>l.value.includes(t.key)),Q=e=>e.length>0&&e.every(t=>l.value.includes(t.key)),W=e=>{const t=P(e).length;return t>0&&t<e.length},H=(e,t)=>{t.stopPropagation();const u=t.target.checked,c=e.map(_=>_.key);if(u){const _=[...l.value];c.forEach(q=>{_.includes(q)||_.push(q)}),l.value=_}else l.value=l.value.filter(_=>!c.includes(_))},K=e=>{s.value.has(e)?s.value.delete(e):s.value.add(e)},X=()=>{h("confirm",[...l.value]),h("update:modelValue",!1)},Y=()=>{l.value=[...d.selectedToolIds],h("update:modelValue",!1)};return M(m,e=>{if(e){s.value.clear();const t=Array.from($.value.keys());t.length>1&&t.slice(1).forEach(a=>{s.value.add(a)})}}),(e,t)=>(p(),x(me,{modelValue:m.value,"onUpdate:modelValue":[t[4]||(t[4]=a=>m.value=a),Y],title:e.$t("toolSelection.title"),onConfirm:X},{default:D(()=>[o("div",ve,[o("div",he,[o("div",fe,[F(o("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>y.value=a),type:"text",class:"search-input",placeholder:e.$t("toolSelection.searchPlaceholder")},null,8,ge),[[oe,y.value]])]),o("div",ye,[F(o("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>i.value=a),class:"sort-select"},[o("option",ke,r(e.$t("toolSelection.sortByGroup")),1),o("option",_e,r(e.$t("toolSelection.sortByName")),1),o("option",be,r(e.$t("toolSelection.sortByStatus")),1)],512),[[te,i.value]])])]),o("div",Te,[o("span",$e,r(e.$t("toolSelection.summary",{groups:$.value.size,tools:j.value,selected:l.value.length})),1)]),$.value.size>0?(p(),f("div",Ce,[(p(!0),f(L,null,U($.value,([a,u])=>(p(),f("div",{key:a,class:"tool-group"},[o("div",{class:I(["tool-group-header",{collapsed:s.value.has(a)}]),onClick:c=>K(a)},[o("div",we,[k(S(w),{icon:s.value.has(a)?"carbon:chevron-right":"carbon:chevron-down",class:"collapse-icon"},null,8,["icon"]),k(S(w),{icon:"carbon:folder",class:"group-icon"}),o("span",Ve,r(a),1),o("span",Be," ("+r(P(u).length)+"/"+r(u.length)+") ",1)]),o("div",{class:"group-actions",onClick:t[2]||(t[2]=G(()=>{},["stop"]))},[o("label",Ie,[o("input",{type:"checkbox",class:"group-enable-checkbox",checked:Q(u),onChange:c=>H(u,c),"data-group":a},null,40,Me),o("span",Ge,r(e.$t("toolSelection.enableAll")),1)])])],10,Se),o("div",{class:I(["tool-group-content",{collapsed:s.value.has(a)}])},[(p(!0),f(L,null,U(u.filter(c=>c&&c.key),c=>(p(),f("div",{key:c.key,class:"tool-selection-item"},[o("div",Ae,[o("div",Ee,r(c.name),1),c.description?(p(),f("div",Le,r(c.description),1)):V("",!0)]),o("div",Ue,[o("label",{class:"tool-enable-switch",onClick:t[3]||(t[3]=G(()=>{},["stop"]))},[o("input",{type:"checkbox",class:"tool-enable-checkbox",checked:J(c.key),onChange:_=>O(c.key,_)},null,40,xe),t[5]||(t[5]=o("span",{class:"tool-enable-slider"},null,-1))])])]))),128))],2)]))),128))])):(p(),f("div",De,[k(S(w),{icon:"carbon:tools",class:"empty-icon"}),o("p",null,r(e.$t("toolSelection.noToolsFound")),1)]))])]),_:1},8,["modelValue","title"]))}}),Ze=E(Pe,[["__scopeId","data-v-8846e83c"]]),qe={class:"assigned-tools"},ze={class:"section-header"},Fe={class:"tool-info"},Ne={class:"tool-name"},Re={class:"tool-desc"},je={key:0,class:"no-tools"},Je=A({__name:"AssignedTools",props:{title:{},selectedToolIds:{},availableTools:{},addButtonText:{default:"Add/Remove Tools"},emptyText:{default:"No tools assigned"},showAddButton:{type:Boolean,default:!0},useGridLayout:{type:Boolean,default:!1}},emits:["add-tools","tools-filtered"],setup(n,{emit:v}){const d=n,h=v,m=B(()=>d.selectedToolIds.filter(s=>d.availableTools.some(l=>l.key===s)));M(m,s=>{s.length!==d.selectedToolIds.length&&h("tools-filtered",s)},{immediate:!0});const y=s=>{const l=d.availableTools.find(g=>g.key===s);return l?l.name:s},i=s=>{const l=d.availableTools.find(g=>g.key===s);return l?l.description:""};return(s,l)=>(p(),f("div",qe,[o("div",ze,[o("span",null,r(s.title)+" ("+r(m.value.length)+")",1),s.showAddButton?(p(),f("button",{key:0,class:"action-btn small",onClick:l[0]||(l[0]=g=>s.$emit("add-tools"))},[k(S(w),{icon:"carbon:add"}),le(" "+r(s.addButtonText),1)])):V("",!0)]),o("div",{class:I(["tools-grid",{"grid-layout":s.useGridLayout}])},[(p(!0),f(L,null,U(m.value,g=>(p(),f("div",{key:g,class:"tool-item assigned"},[o("div",Fe,[o("span",Ne,r(y(g)),1),o("span",Re,r(i(g)),1)])]))),128)),m.value.length===0?(p(),f("div",je,[k(S(w),{icon:"carbon:tool-box"}),o("span",null,r(s.emptyText),1)])):V("",!0)],2)]))}}),eo=E(Je,[["__scopeId","data-v-ba0c6f80"]]),Oe={class:"toast-header"},Qe={class:"toast-title"},We={class:"toast-content"},He=A({__name:"Toast",setup(n,{expose:v}){const d=b(!1),h=b(""),m=b("success"),y=b("carbon:checkmark"),i=b(3e3),s=(g,T="success",$=3e3)=>{h.value=g,m.value=T,y.value=T==="success"?"carbon:checkmark":"carbon:error",i.value=$,d.value=!0},l=()=>{d.value=!1};return v({show:s}),(g,T)=>(p(),x(N,{to:"body"},[k(R,{name:"modal"},{default:D(()=>[d.value?(p(),f("div",{key:0,class:"toast-overlay",onClick:l},[o("div",{class:I(["toast-modal",`toast--${m.value}`]),onClick:T[0]||(T[0]=G(()=>{},["stop"]))},[o("div",Oe,[k(S(w),{icon:y.value,class:"toast-icon"},null,8,["icon"]),o("span",Qe,r(m.value==="success"?"Success":"Error"),1)]),o("div",We,[o("span",null,r(h.value),1)]),o("div",{class:"toast-actions"},[o("button",{class:"toast-btn toast-btn--primary",onClick:l}," Confirm ")])],2)])):V("",!0)]),_:1})]))}}),Ke=E(He,[["__scopeId","data-v-7dea5631"]]);let C=null;const oo=()=>{if(!C){const n=ae(Ke),v=document.createElement("div");document.body.appendChild(v),C=n.mount(v)}return{success:(n,v)=>{C==null||C.show(n,"success",v)},error:(n,v)=>{C==null||C.show(n,"error",v)}}};function to(){const n=ne({show:!1,text:"",type:"success"});return{message:n,showMessage:(d,h="success")=>{console.log(`Showing message: ${d}, Type: ${h}`),n.text=d,n.type=h,n.show=!0;const m=h==="error"?5e3:3e3;console.log(`Message will be automatically hidden after ${m}ms`),setTimeout(()=>{n.show=!1,console.log("Message hidden")},m)}}}export{eo as A,me as M,Ze as T,to as a,oo as u};
