# Maven
target/
!target/*.jar
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
h2-data/
playwright/

# Git
.git/
.gitignore

# Docker
.dockerignore
docker-compose.yml

# Documentation
README*.md
DOCKER_BUILD.md
*.md

# Scripts (keep build-ui.sh as it's needed)
*.bat
script/

# Environment files
.env
.env.*

# Test files
src/test/

# UI build artifacts (will be rebuilt in container)
ui-vue3/node_modules/
ui-vue3/dist/
dist/
