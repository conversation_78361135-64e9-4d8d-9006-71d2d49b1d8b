# 中文消息文件

# ManusProperties - 浏览器设置
manus.browser.headless.description=是否使用无头浏览器模式
manus.browser.headless.option.true=是
manus.browser.headless.option.false=否
manus.browser.requestTimeout.description=浏览器请求超时时间(秒)

# ManusProperties - 通用设置
manus.general.debugDetail.description=debug模式 ：会要求模型输出更多内容，方便查找问题，但速度更慢
manus.general.debugDetail.option.true=是
manus.general.debugDetail.option.false=否
manus.general.baseDir.description=manus根目录

# ManusProperties - 交互设置
manus.interaction.openBrowser.description=启动时自动打开浏览器
manus.interaction.openBrowser.option.true=是
manus.interaction.openBrowser.option.false=否

# ManusProperties - 智能体设置
manus.agent.maxSteps.description=智能体执行最大步数
manus.agent.forceOverrideFromYaml.description=强制使用YAML配置文件覆盖同名Agent
manus.agent.forceOverrideFromYaml.option.true=是
manus.agent.forceOverrideFromYaml.option.false=否
manus.agent.userInputTimeout.description=用户输入表单等待超时时间(秒)
manus.agent.maxMemory.description=能记住的最大消息数
manus.agent.parallelToolCalls.description=并行工具调用
manus.agent.parallelToolCalls.option.true=是
manus.agent.parallelToolCalls.option.false=否

# ManusProperties - 无限上下文设置
manus.infiniteContext.enabled.description=是否开启无限上下文
manus.infiniteContext.enabled.option.true=是
manus.infiniteContext.enabled.option.false=否
manus.infiniteContext.parallelThreads.description=并行处理线程数
manus.infiniteContext.taskContextSize.description=触发无限上下文的字符数阈值(字符数)

# ManusProperties - 文件系统设置
manus.filesystem.allowExternalAccess.description=是否允许在工作目录外进行文件操作
manus.filesystem.allowExternalAccess.option.true=是
manus.filesystem.allowExternalAccess.option.false=否

# ManusProperties - MCP服务加载器设置
manus.mcpServiceLoader.connectionTimeoutSeconds.description=MCP连接超时时间(秒)
manus.mcpServiceLoader.maxRetryCount.description=MCP连接最大重试次数
manus.mcpServiceLoader.maxConcurrentConnections.description=MCP最大并发连接数
