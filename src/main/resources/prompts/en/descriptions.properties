PLANNING_PLAN_CREATION=Prompt for building execution plans, adjust this if task decomposition is not working well
AGENT_CURRENT_STEP_ENV=Defines current environment information, corresponding to results from all functions called by agent in the past, stored separately in agent
AGENT_STEP_EXECUTION=Context information given to agent during each execution step, most variables are preset and shouldn't be changed, can adjust some suggestions, a key agent step execution prompt
PLANNING_PLAN_FINALIZER=Prompt for final summary, corresponds to the action of informing users after task completion, merged with user request information
DIRECT_RESPONSE=Prompt for direct response mode, directly returns results when user requests don't need complex planning
AGENT_STUCK_ERROR=Error message when agent execution gets stuck
SUMMARY_PLAN_TEMPLATE=JSON template for content summary execution plan
MAPREDUCE_TOOL_DESCRIPTION=Description for MapReduce planning tool
MAPREDUCE_TOOL_PARAMETERS=Parameter definition JSON for MapReduce planning tool
AGENT_DEBUG_DETAIL_OUTPUT=Detailed output requirements for agent debug mode
AGENT_NORMAL_OUTPUT=Output requirements for agent normal mode
AGENT_PARALLEL_TOOL_CALLS_RESPONSE=Response rules for agent parallel tool calls
FORM_INPUT_TOOL_DESCRIPTION=Description for form input tool
FORM_INPUT_TOOL_PARAMETERS=Parameter definition JSON for form input tool

# Tool Descriptions
BASH_TOOL_DESCRIPTION=Description for bash command execution tool
BASH_TOOL_PARAMETERS=Parameter definition JSON for bash command execution tool
TEXTFILEOPERATOR_TOOL_DESCRIPTION=Description for text file operation tool
TEXTFILEOPERATOR_TOOL_PARAMETERS=Parameter definition JSON for text file operation tool
BROWSER_USE_TOOL_DESCRIPTION=Description for browser automation tool
BROWSER_USE_TOOL_PARAMETERS=Parameter definition JSON for browser automation tool
PYTHON_EXECUTE_TOOL_DESCRIPTION=Description for Python code execution tool
PYTHON_EXECUTE_TOOL_PARAMETERS=Parameter definition JSON for Python code execution tool
DATABASE_USE_TOOL_DESCRIPTION=Description for database operation tool
DATABASE_USE_TOOL_PARAMETERS=Parameter definition JSON for database operation tool
CRON_TOOL_TOOL_DESCRIPTION=Description for scheduled task tool
CRON_TOOL_TOOL_PARAMETERS=Parameter definition JSON for scheduled task tool
INNER_STORAGE_CONTENT_TOOL_TOOL_DESCRIPTION=Description for internal storage content tool
INNER_STORAGE_CONTENT_TOOL_TOOL_PARAMETERS=Parameter definition JSON for internal storage content tool
DOC_LOADER_TOOL_DESCRIPTION=Description for document loader tool
DOC_LOADER_TOOL_PARAMETERS=Parameter definition JSON for document loader tool
FILE_MERGE_TOOL_TOOL_DESCRIPTION=Description for file merge tool
FILE_MERGE_TOOL_TOOL_PARAMETERS=Parameter definition JSON for file merge tool
DATA_SPLIT_TOOL_DESCRIPTION=Description for data split tool
DATA_SPLIT_TOOL_PARAMETERS=Parameter definition JSON for data split tool
MAP_OUTPUT_TOOL_DESCRIPTION=Description for map output tool
MAP_OUTPUT_TOOL_PARAMETERS=Parameter definition JSON for map output tool
REDUCE_OPERATION_TOOL_DESCRIPTION=Description for reduce operation tool
REDUCE_OPERATION_TOOL_PARAMETERS=Parameter definition JSON for reduce operation tool
FINALIZE_TOOL_DESCRIPTION=Description for finalize tool
FINALIZE_TOOL_PARAMETERS=Parameter definition JSON for finalize tool
TERMINATE_TOOL_DESCRIPTION=Description for terminate tool
TERMINATE_TOOL_PARAMETERS=Parameter definition JSON for terminate tool
PPT_GENERATOR_TOOL_DESCRIPTION=Description for PPT generator tool
PPT_GENERATOR_TOOL_PARAMETERS=Parameter definition JSON for PPT generator tool
