[{"name": "java.lang.String", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "java.lang.Object", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "java.util.ArrayList", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "java.util.HashMap", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.h2.Driver", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.h2.Driver", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.postgresql.Driver", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.mysql.cj.jdbc.Driver", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.config.ManusProperties", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.config.IConfigService", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.config.ConfigService", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.aop.framework.CglibAopProxy", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.aop.framework.CglibAopProxy$SerializableNoOp", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.Dispatcher", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.MethodInterceptor", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.Enhancer", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.boot.context.properties.ConfigurationPropertiesBean", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.dynamic.agent.service.DynamicAgentLoader", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.dynamic.agent.service.AgentServiceImpl", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.planning.PlanningFactory", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.llm.LlmService", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.Enhancer", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.MethodProxy", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.Callback", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "allDeclaredFields": true}, {"name": "org.springframework.cglib.proxy.Factory", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.impl.Message", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.impl.SerializedArgument", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.impl.SerializedValue", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.impl.SerializedValue$O", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Browser$CloseOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Browser$NewContextOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Browser$NewPageOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Browser$StartTracingOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.impl.driver.jar.DriverJar", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$CheckOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$ClickOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$DblclickOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$FillOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$HoverOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$InputValueOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$PressOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$ScreenshotOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$ScrollIntoViewIfNeededOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$SelectTextOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$SetCheckedOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$SetInputFilesOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$TapOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$TypeOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$UncheckOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$WaitForElementStateOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.ElementHandle$WaitForSelectorOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.options.HttpHeader", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.options.Timing", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.options.ViewportSize", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$AddScriptTagOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$AddStyleTagOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$CheckOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$ClickOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$DblclickOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$DispatchEventOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$FillOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$FocusOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$GetAttributeOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$GetByRoleOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$GetByTextOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$HoverOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$InnerHTMLOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$InnerTextOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$InputValueOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$IsVisibleOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$LocatorOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$PressOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$SelectOptionOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$SetContentOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$SetInputFilesOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$TapOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$TextContentOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$TypeOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$UncheckOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$UncheckOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$WaitForFunctionOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$WaitForLoadStateOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$WaitForSelectorOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$WaitForURLOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.Frame$NavigateOptions", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "com.microsoft.playwright.impl.PlaywrightImpl", "allPublicMethods": true, "allPublicConstructors": true, "allPublicFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true, "unsafeAllocated": true, "allDeclaredFields": true}, {"name": "java.lang.String", "allPublicMethods": true}, {"name": "java.lang.Object", "allPublicMethods": true}, {"name": "java.util.ArrayList", "allPublicMethods": true}, {"name": "java.util.HashMap", "allPublicMethods": true}, {"name": "com.h2.Driver", "allPublicMethods": true, "allPublicConstructors": true}, {"name": "org.h2.Driver", "allPublicMethods": true, "allPublicConstructors": true}, {"name": "org.postgresql.Driver", "allPublicMethods": true, "allPublicConstructors": true}, {"name": "com.mysql.cj.jdbc.Driver", "allPublicMethods": true, "allPublicConstructors": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.PlanningTool$PlanningInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.DocLoaderTool$DocLoaderInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.searchAPI.GoogleSearch$GoogleSearchInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.mapreduce.MapReduceTool$MapReduceInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.bash.Bash$BashInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.code.PythonExecute$PythonInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.textOperator.TextFileOperator$TextFileInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.innerStorage.InnerStorageContentTool$InnerStorageContentInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.FormInputTool$UserFormInput", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.FormInputTool$InputItem", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.browser.actions.BrowserRequestVO", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.searchAPI.serpapi.SerpApiService$Request", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.searchAPI.serpapi.SerpApiService$Response", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}, {"name": "com.alibaba.cloud.ai.example.manus.tool.searchAPI.serpapi.SerpApiService$SearchResult", "allPublicMethods": true, "allPublicConstructors": true, "allDeclaredFields": true}]