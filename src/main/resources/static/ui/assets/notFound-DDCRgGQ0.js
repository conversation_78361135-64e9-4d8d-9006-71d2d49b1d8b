import{_ as n}from"./Java-AI-BYpq8IxI.js";import{d as c,a as i,b as d,e as o,t,g as l,h as m,i as p,x as _}from"./index-CoxzeSM8.js";import{I as u,_ as f}from"./_plugin-vue_export-helper-Pwn8nLHl.js";const b={class:"not-found-page"},g={class:"error-container"},h={class:"error-message"},k=c({__name:"notFound",setup(v){const r=_(),a=()=>{r.push("/home")};return(e,s)=>(d(),i("div",b,[o("div",g,[s[0]||(s[0]=o("div",{class:"error-icon"},[o("img",{src:n,alt:"Java-AI",width:"96",height:"96",class:"java-logo"})],-1)),s[1]||(s[1]=o("h1",{class:"error-code"},"404",-1)),o("p",h,t(e.$t("error.notFound")),1),o("button",{class:"back-button",onClick:a},[l(p(u),{icon:"carbon:arrow-left"}),m(" "+t(e.$t("error.backToHome")),1)])])]))}}),F=f(k,[["__scopeId","data-v-57698550"]]);export{F as default};
