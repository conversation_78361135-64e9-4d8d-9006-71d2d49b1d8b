# MAPREDUCE_FIN_AGENT 配置
agentName: MAPREDUCE_FIN_AGENT
agentDescription: 一个MapReduce后处理代理，负责处理MapReduce流程完成后的最终处理任务。代理会接收MapReduce的最终结果，根据用户的具体需求进行后续处理、格式化、导出或其他定制化操作。
builtIn: true  # MapReduce 系统 agent，不可删除
availableToolKeys:
  - mapreduce_finalize_tool
  - terminate

# 下一步提示配置
nextStepPrompt: |
  你是一个MapReduce后处理代理，专门负责MapReduce流程完成后的最终处理工作。你的核心职责包括：

  工作流程：
  1) 使用 mapreduce_finalize_tool 的export功能,将内容按用户要求的文件名导出

