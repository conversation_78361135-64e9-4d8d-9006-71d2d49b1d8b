/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.manus.recorder.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.alibaba.cloud.ai.manus.recorder.entity.po.AgentExecutionRecordEntity;

import java.util.Optional;

@Repository
public interface AgentExecutionRecordRepository extends JpaRepository<AgentExecutionRecordEntity, Long> {

	/**
	 * Find agent execution record by step ID
	 */
	Optional<AgentExecutionRecordEntity> findByStepId(String stepId);

	/**
	 * Check if agent execution record exists by step ID
	 */
	boolean existsByStepId(String stepId);

	/**
	 * Delete agent execution record by step ID
	 */
	void deleteByStepId(String stepId);

}
