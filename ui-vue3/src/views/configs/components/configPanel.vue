<!--
  Copyright 2025 the original author or authors.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!--
  Component for displaying a configuration panel with a title, actions, and body content.
  
  @slot title - The title of the panel.
  @slot actions - Actions to be displayed alongside the title.
  @slot default - The main content of the panel.
-->

<template>
  <div class="config-config">
    <div class="panel-header">
      <slot name="title"></slot>

      <div class="panel-actions">
        <slot name="actions"></slot>
      </div>
    </div>

    <slot></slot>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
.config-config {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-actions {
  display: flex;
  gap: 12px;
}

:deep(.panel-header h2) {
  margin-bottom: 0 !important;
}
</style>
