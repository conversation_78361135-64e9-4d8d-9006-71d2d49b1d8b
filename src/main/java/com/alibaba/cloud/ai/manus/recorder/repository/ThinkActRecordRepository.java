/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.manus.recorder.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.alibaba.cloud.ai.manus.recorder.entity.po.ThinkActRecordEntity;

import java.util.List;
import java.util.Optional;

@Repository
public interface ThinkActRecordRepository extends JpaRepository<ThinkActRecordEntity, Long> {

	/**
	 * Find think-act record by parent execution ID
	 */
	List<ThinkActRecordEntity> findByParentExecutionId(Long parentExecutionId);

	/**
	 * Find think-act record by ID
	 */
	Optional<ThinkActRecordEntity> findById(Long id);

	/**
	 * Find think-act record by tool call ID through ActToolInfo relationship
	 */
	@Query("SELECT t FROM ThinkActRecordEntity t JOIN t.actToolInfoList a WHERE a.toolCallId = :toolCallId")
	Optional<ThinkActRecordEntity> findByActToolInfoToolCallId(@Param("toolCallId") String toolCallId);

}
