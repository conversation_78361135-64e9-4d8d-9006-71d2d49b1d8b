/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { I18nType } from './type.ts'

const words: I18nType = {
  // Basic navigation
  conversation: 'Conversation',
  plan: 'Plan Execution',
  backHome: 'Back to Home',
  noPageTip: 'The page you are looking for does not exist.',

  // Initialization page
  init: {
    welcome: 'Welcome to JManus',
    welcomeStep: 'Welcome to JManus',
    description: 'To get started, you need to configure an LLM service to enable AI features. You can choose Alibaba Cloud DashScope or configure any OpenAI-compatible API service.',
    languageStepDescription: 'Please select your language preference, which will be used as the default interface language.',
    stepLanguage: 'Language',
    stepModel: 'Model Setup',
    selectLanguageLabel: 'Select Language',
    continueToModel: 'Continue to Model Setup',
    back: 'Back',
    configModeLabel: 'Configuration Mode',
    dashscopeMode: 'Alibaba Cloud DashScope (Recommended)',
    dashscopeModeDesc: 'Use Alibaba Cloud DashScope service, just provide API key to get started quickly',
    customMode: 'Custom OpenAI Compatible Service',
    customModeDesc: 'Configure any OpenAI API compatible service, such as Ollama, LocalAI, etc.',
    apiKeyLabel: 'DashScope API Key',
    apiKeyPlaceholder: 'Enter your API key',
    apiKeyHint: 'You can get your API key from Alibaba Cloud Bailian Console.',
    getApiKey: 'Get API Key',
    showApiKey: 'Show API Key',
    hideApiKey: 'Hide API Key',
    baseUrlLabel: 'API Base URL',
    baseUrlPlaceholder: 'https://api.openai.com or your custom URL',
    baseUrlHint: 'OpenAI compatible API base URL, e.g. http://localhost:11434',
    customApiKeyLabel: 'API Key',
    customApiKeyPlaceholder: 'Enter your API key',
    modelNameLabel: 'Model Name',
    modelNamePlaceholder: 'gpt-4.1 or your model name',
    modelNameHint: 'Enter the model name you want to use, e.g. gemini-2.5-pro, gpt-4.1, etc.',
    modelDisplayNameLabel: 'Model Display Name (Optional)',
    modelDisplayNamePlaceholder: 'Display name for the model',
    saveAndContinue: 'Save and Continue',
    saving: 'Saving...',
    apiKeyRequired: 'API key is required',
    baseUrlRequired: 'API base URL is required',
    modelNameRequired: 'Model name is required',
    saveFailed: 'Failed to save configuration',
    networkError: 'Network error, please check your internet connection',
    successMessage: 'Configuration saved successfully! Redirecting to home page...',
    restartRequired: 'API key saved successfully! The application needs to be restarted for the configuration to take effect.\n\nClick "OK" to restart now, or "Cancel" to restart manually later.',
    simplifiedChinese: 'Simplified Chinese',
    completionsPath: 'Completions Path',
    completionsPathPlaceholder: 'If not specified, the default configuration is: /v1/chat/completions',
  },

  // Common buttons and actions
  common: {
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    edit: 'Edit',
    save: 'Save',
    reset: 'Reset',
    close: 'Close',
    add: 'Add',
    create: 'Create',
    update: 'Update',
    submit: 'Submit',
    clear: 'Clear',
    submitFailed: 'Submit Failed',
    unknownError: 'Unknown Error',
    search: 'Search',
    loading: 'Loading...',
    parameters: 'Parameters',
  },

  // File Browser
  fileBrowser: {
    title: 'File Browser',
    refresh: 'Refresh',
    loading: 'Loading file tree...',
    retry: 'Retry',
    noFiles: 'No files found',
    loadError: 'Failed to load file tree',
    loadingContent: 'Loading file content...',
    contentLoadError: 'Failed to load file content',
    download: 'Download',
    downloadToView: 'Download to View',
    binaryFile: 'This is a binary file that cannot be displayed in the viewer.',
    open: 'Open',
    copyPath: 'Copy Path',
    noPlanSelected: 'No plan selected. Please execute a task to view files.',
    noFilesYet: 'No Files Generated Yet',
    waitingForFiles: 'The AI model is still processing your request. Files will appear here once they are generated.',
    filesTip: 'Generated files like analysis results, reports, and data exports will be displayed in this file browser.',
    noPlanExecuting: 'No task is currently being executed.',
    startTaskTip: 'Please start a task in the chat panel on the left. Generated files will be displayed here.',
    waitingForGeneration: 'Waiting for File Generation',
    planExecuting: 'The AI model is currently executing the plan and generating files.',
    checking: 'Checking...',
    checkNow: 'Check Now',
  },

  // Language
  language: {
    zh: '中文',
    en: 'English',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    yes: 'Yes',
    no: 'No',
    enable: 'Enable',
    disable: 'Disable',
    published: 'Published',
    unpublished: 'Unpublished',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    undo: 'Undo',
    redo: 'Redo',
    select: 'Select',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    previous: 'Previous',
    next: 'Next',
    finish: 'Finish',
    retry: 'Retry',
    refresh: 'Refresh',
    import: 'Import',
    export: 'Export',
    upload: 'Upload',
    download: 'Download',
    preview: 'Preview',
    expand: 'Expand',
    collapse: 'Collapse',
    maximize: 'Maximize',
    minimize: 'Minimize',
    fullscreen: 'Fullscreen',
    exitFullscreen: 'Exit Fullscreen',
    parameters: 'Parameters',
    thinking: 'Thinking',
    input: 'Input',
    total: 'Total',
    loadFailed: 'Load Failed',
    switch: 'Switch Language',
  },

  // Configuration related
  config: {
    title: 'Configuration Management',
    loading: 'Loading configuration...',
    notFound: 'No configuration items found',
    reset: 'Reset',
    resetGroupConfirm: 'Reset all configurations in this group to default values',
    modified: 'Modified',
    saved: 'Configuration saved',
    saveFailed: 'Save failed',
    search: 'Search configuration items...',
    mcpSearch: 'Search MCP servers...',
    mcpConfigPlaceholder: 'Please enter MCP server configuration (JSON format)...',
    types: {
      string: 'String',
      text: 'Text',
      number: 'Number',
      boolean: 'Boolean',
      select: 'Select',
      textarea: 'Textarea',
      checkbox: 'Checkbox',
    },
    range: 'Range',
    min: 'Minimum',
    max: 'Maximum',
    categories: {
      basic: 'Basic Configuration',
      agent: 'Agent Configuration',
      model: 'Model Configuration',
      mcp: 'Tools/MCP Configuration',
      prompt: 'Dynamic Prompt Configuration',
      namespace: 'Namespace Configuration',
    },
    subGroupDisplayNames: {
      agent: 'Agent',
      browser: 'Browser',
      interaction: 'Interaction',
      system: 'System',
      performance: 'Performance',
      general: 'General',
      agents: 'Multi - Agent',
      infiniteContext: 'Infinite Context',
      filesystem: 'File System',
      mcpServiceLoader: 'MCP Service Loader',
    },
    // Agent configuration page
    agentConfig: {
      title: 'Agent Configuration',
      import: 'Import',
      export: 'Export',
      configuredAgents: 'Configured Agents',
      agentCount: 'agents',
      noAgent: 'No agent configuration',
      createNew: 'Create New Agent',
      selectAgentHint: 'Please select an agent to configure',
      newAgent: 'New Agent',
      agentName: 'Agent Name',
      agentNamePlaceholder: 'Enter agent name',
      description: 'Description',
      descriptionPlaceholder: 'Describe the function and purpose of this agent',
      nextStepPrompt: 'Agent Prompt (personality, requirements, and next step guidance)',
      nextStepPromptPlaceholder:
        "Set the agent's personality, requirements, and next step guidance...",
      toolConfiguration: 'Tool Configuration',
      assignedTools: 'Assigned Tools',
      noAssignedTools: 'No assigned tools',
      addRemoveTools: 'Add/Remove Tools',
      deleteConfirm: 'Delete Confirmation',
      deleteConfirmText: 'Are you sure you want to delete',
      deleteWarning: 'This operation cannot be undone.',
      requiredFields: 'Please fill in the required fields',
      createSuccess: 'Agent created successfully',
      createFailed: 'Failed to create agent',
      saveSuccess: 'Agent saved successfully',
      saveFailed: 'Failed to save agent',
      deleteSuccess: 'Agent deleted successfully',
      deleteFailed: 'Failed to delete agent',
      importSuccess: 'Agent imported successfully',
      importFailed: 'Failed to import agent',
      exportSuccess: 'Agent exported successfully',
      exportFailed: 'Failed to export agent',
      loadDataFailed: 'Failed to load data',
      loadDetailsFailed: 'Failed to load agent details',
      invalidFormat: 'Invalid agent configuration format: missing required fields',
      modelConfiguration: 'Model Configuration',
      modelConfigurationLabel: 'Select Model',
      cannotDeleteBuiltIn: 'Cannot delete built-in agent',
      builtInAgents: 'Built-in Agents',
      customAgents: 'Custom Agents',
    },
    // Model configuration page
    modelConfig: {
      title: 'Model Configuration',
      import: 'Import',
      export: 'Export',
      configuredModels: 'Configured Models',
      modelCount: 'models',
      noModel: 'No model configurations available',
      createNew: 'Create New Model',
      selectModelHint: 'Please select a model to configure',
      newModel: 'New Model',
      type: 'Model Type',
      typePlaceholder: 'Select model type',
      baseUrl: 'Base Url',
      baseUrlPlaceholder: 'Enter base url',
      apiKey: 'API Key',
      apiKeyPlaceholder: 'Enter API key',
      showApiKey: 'Show API Key',
      hideApiKey: 'Hide API Key',
      modelName: 'Model Name',
      modelNamePlaceholder: 'Enter model name',
      description: 'Description',
      descriptionPlaceholder: 'Enter model description',
      deleteConfirm: 'Delete Confirmation',
      deleteConfirmText: 'Are you sure you want to delete',
      deleteWarning: 'This action cannot be undone.',
      requiredFields: 'Please fill in all required fields',
      createSuccess: 'Model created successfully',
      createFailed: 'Failed to create model',
      saveSuccess: 'Model saved successfully',
      saveFailed: 'Failed to save model',
      deleteSuccess: 'Model deleted successfully',
      deleteFailed: 'Failed to delete model',
      importSuccess: 'Model imported successfully',
      importFailed: 'Failed to import model',
      exportSuccess: 'Model exported successfully',
      exportFailed: 'Failed to export model',
      loadDataFailed: 'Failed to load data',
      loadDetailsFailed: 'Failed to load model details',
      invalidFormat: 'Model configuration format is invalid: missing required fields',
      validateConfig: 'Validate Config',
      validationSuccess: 'Validation successful',
      validationFailed: 'Validation failed',
      pleaseEnterBaseUrlAndApiKey: 'Please enter Base URL and API Key',
      selectModel: 'Select Model',
      availableModels: 'Available Models',
      searchModels: 'Search models...',
      getModelsCount: 'Found {count} available models',
      default: 'Default',
      setAsDefault: 'Set as Default',
      currentDefault: 'Current Default',
      setDefaultSuccess: 'Model set as default successfully',
      setDefaultFailed: 'Failed to set model as default',
      validatingBeforeSave: 'Validating API key before saving...',
      validationFailedCannotSave: 'API key validation failed, cannot save',
      temperature: 'Temperature',
      temperaturePlaceholder: 'Leave empty to use model default',
      topP: 'Top P',
      topPPlaceholder: 'Leave empty to use model default',
      completionsPath: 'Completions Path',
      completionsPathPlaceholder: 'If not specified, the default configuration is: /v1/chat/completions',
      headers: 'Headers',
      headersPlaceholder: 'Request headers (JSON format)',
    },
    // MCP configuration page
    mcpConfig: {
      title: 'MCP Server Configuration',
      mcpServers: 'MCP Servers',
      addMcpServer: 'Add MCP Server',
      serverList: 'Server List',
      noServers: 'No MCP server configuration',
      connectionType: 'Connection Type',
      configJsonLabel: 'MCP JSON Configuration:',
      configJsonPlaceholder: 'Please enter MCP server configuration (JSON format)...',
      instructions: 'Instructions:',
      instructionStep1: 'Find the configuration JSON for your MCP server:',
      instructionStep1Local: 'Local (STDIO)',
      instructionStep1LocalDesc:
        'Available at mcp.so, requires Node.js environment and understanding of each item in the configuration JSON for proper adjustments like setting access keys',
      instructionStep1Remote: 'Remote Service (SSE/STREAMING)',
      instructionStep1RemoteDesc:
        'Available at mcp.higress.ai/, offers SSE and STREAMING types, currently STREAM protocol is more complete',
      instructionStep2:
        'Copy the JSON configuration to the input box above, select STUDIO for local, STREAMING or SSE for remote, then submit',
      instructionStep3: 'This will successfully register the MCP tools.',
      instructionStep4:
        'Then you need to create a new agent in Agent Configuration, and add the specific MCP tools you just added, which can greatly reduce conflicts and enhance the accuracy of tools being selected by agents',
      configRequired: 'Please enter MCP server configuration',
      invalidJson: 'Configuration JSON format is incorrect, please check syntax',
      addFailed: 'Failed to add MCP server, please try again',
      deleteFailed: 'Failed to delete MCP server, please try again',
      formatJson: 'Format',
      jsonStatusEmpty: 'Please enter JSON configuration',
      jsonStatusValid: 'JSON format is valid',
      jsonStatusInvalid: 'JSON format is invalid',
      missingMcpServers: '❌ Missing mcpServers property - Please ensure JSON contains mcpServers object',
      invalidServerConfig: '❌ Invalid server configuration: {serverId} - Server config must be an object',
      invalidArgs: '❌ args field must be an array: {serverId} - Please change args to array format',
      invalidEnv: '❌ env field must be an object: {serverId} - Please change env to object format',
      invalidArgsType: '❌ args array elements must be strings: {serverId}, index: {index} - Please ensure all arguments are strings',
      invalidEnvType: '❌ env object values must be strings: {serverId}, key: {key} - Environment variable values must be strings',
      missingUrl: '❌ Missing url field: {serverId} - Must have url when no command is present',
      invalidUrl: '❌ Invalid url format: {serverId} - Please check if URL format is correct',
      studioExample:
        'Please enter MCP server configuration JSON.\n\nExample:\n{\n  "mcpServers": {\n    "github": {\n      "command": "npx",\n      "args": [\n        "-y",\n        "@modelcontextprotocol/server-github"\n      ],\n      "env": {\n        "GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"\n      }\n    }\n  }\n}',
      sseExample:
        'Please enter SSE MCP server configuration JSON.\n\nExample:\n{\n  "mcpServers": {\n    "remote-server": {\n      "url": "https://example.com/mcp",\n      "headers": {\n        "Authorization": "Bearer <YOUR_TOKEN>"\n      }\n    }\n  }\n}',
      selectServerHint: 'Please select an MCP server on the left or click to create a new MCP configuration',
      jsonEditor: 'JSON Editor',
      jsonConfigEmpty: 'JSON configuration cannot be empty',
      jsonFormatError: 'JSON format error',
      jsonConfigSaved: 'JSON configuration saved',
      confirmDelete: 'Confirm Delete',
      deleteConfirmMessage: 'Are you sure you want to delete this MCP server configuration? This action cannot be undone.',
      deleteWarningText: 'This action cannot be undone. Please proceed with caution.',
      noServerSelected: 'No MCP server selected',
      updateSuccess: 'Updated successfully',
      saveFailed: 'Save failed, please retry',
      operationFailed: 'Operation failed',
      mcpServerNamePlaceholder: 'Please enter MCP server name',
      enabled: 'Enabled',
      disabled: 'Disabled',
      newMcpConfig: 'New MCP Config',
      importAll: 'Import All',
      exportAll: 'Export All',
      mcpName: 'MCP Name',
      usageInstructions: 'Usage Instructions',
      getMcpServiceList: 'Get MCP Service List',
      findMcpServices: 'You can find available MCP services on mcp.higress.ai, mcp.so, modelscope.cn',
      batchImportTip: 'If you need to batch configure MCP services, you can use the import all function in the top right corner',
      configureMcpService: 'Configure MCP Service',
      fillServiceName: 'Fill in service name',
      selectConnectionType: 'Select connection type: choose STUDIO for local and input Command, Args and Env. Choose SSE or STREAMING for remote and input URL',
      clickSaveToComplete: 'Click save button to complete MCP configuration, MCP tools will be automatically registered to the system',
      configureAgentUsage: 'Configure Agent Usage',
      createAgentTip: 'Create a new Agent on the Agent configuration page, add the just configured MCP service to the Agent, this can reduce tool conflicts and improve Agent tool selection accuracy',
      copyJsonConfig: 'Copy the complete JSON configuration to the input box above (you can refer to configuration examples), then click import',
      command: 'Command',
      args: 'Args',
      env: 'Env',
      url: 'URL',
      save: 'Save',
      delete: 'Delete',
      reset: 'Reset',
      import: 'Import',
      cancel: 'Cancel',
      exportSuccess: 'Export successful',
      exportFailed: 'Export failed',
      statusToggleSuccess: 'Status toggle successful',
      statusToggleFailed: 'Status toggle failed',
      missingUrlField: 'Missing url field: {serverId} - must have url or baseUrl when no command',
      urlFieldTip: '💡 Please provide url or baseUrl field',
      serverConfigWarning: 'Server {serverId} has no command but also no url or baseUrl',
      jsonSyntaxError: '❌ JSON syntax error - please check brackets, commas, quotes and other symbols',
      jsonIncomplete: '❌ JSON incomplete - please check if ending brackets or quotes are missing',
      jsonNumberError: '❌ JSON number format error - please check number format',
      jsonStringError: '❌ JSON string format error - please check if quotes are paired',
      jsonSyntaxErrorWithMessage: '❌ JSON syntax error: {message}',
      correctFormatExample: '💡 Correct format example: {"mcpServers": {"server-id": {"name": "Server Name", "url": "Server URL"}}}',
      commandPlaceholder: 'e.g.: uvx',
      urlPlaceholder: 'e.g.: https://mcp.example.com/server',
      argsPlaceholder: 'One parameter per line, e.g.:\n--from\nmysql_mcp_server_pro\n--mode\nstdio',
      envPlaceholder: 'Key-value format, one per line, e.g.:\nMYSQL_HOST:127.0.0.1\nMYSQL_PORT:3306\nMYSQL_USER:root',
      connectionTypePlaceholder: 'Please select connection type',
      argsFormatError: 'Args format error, please enter a valid JSON array',
      envFormatError: 'Env format error, please enter a valid JSON object',
      argsStringError: 'Args format error, each parameter must be a string',
      envStringError: 'Env format error, each value must be a string',
      importSuccess: 'Import successful',
      importFailed: 'Import failed',
      importInvalidJson: 'Imported JSON is invalid',
    },
    // Basic configuration
    basicConfig: {
      title: 'Basic Configuration',
      browserSettings: {
        headless: 'Whether to use headless browser mode',
        requestTimeout: 'Browser request timeout (seconds)',
      },
      general: {
        debugDetail:
          'Debug mode: The model will output more content to facilitate problem - finding, but it will be slower',
        baseDir: 'Manus root directory',
      },
      interactionSettings: {
        openBrowser: 'Automatically open the browser on startup',
      },
      agentSettings: {
        maxSteps: 'Max Steps',
        userInputTimeout: 'User input form waiting timeout (seconds)',
        maxMemory: 'Maximum number of messages that can be remembered',
        parallelToolCalls: 'Parallel tool calls',
      },
      agents: {
        forceOverrideFromYaml:
          'Force override of agents with the same name using the YAML configuration file',
      },
      infiniteContext: {
        enabled: 'Whether to enable infinite context',
        parallelThreads: 'Number of parallel processing threads',
        taskContextSize:
          'Character count threshold for triggering infinite context (number of characters)',
      },
      fileSystem: {
        allowExternalAccess: 'Whether to allow file operations beyond the working directory',
      },
      mcpServiceLoader: {
        connectionTimeoutSeconds: 'MCP connection timeout (seconds)',
        maxRetryCount: 'MCP connection max retry count',
        maxConcurrentConnections: 'MCP max concurrent connections',
      },
      systemSettings: {
        systemName: 'System Name',
        language: 'Language',
        maxThreads: 'Max Threads',
        timeoutSeconds: 'Request Timeout (seconds)',
      },
      totalConfigs: 'Total Configurations',
      modified: 'Modified',
      exportConfigs: 'Export Configurations',
      importConfigs: 'Import Configurations',
      search: 'Search',
      loading: 'Loading',
      saveSuccess: 'Configuration saved successfully',
      exportSuccess: 'Configuration exported successfully',
      exportFailed: 'Failed to export configuration',
      invalidFormat: 'Configuration file format is invalid',
      importSuccess: 'Configuration imported successfully',
      resetSuccess: 'Configuration reset successfully',
      notFound: 'No configuration items found',
      noModified: 'No modified configurations',
      resetGroupConfirm: 'Reset all configurations in this group to default values',
      isDefault: 'This group configuration is default',
      reset: 'Reset',
      requestTimeout: 'Request timeout (seconds)',
      browserTimeout: 'Browser request timeout (seconds)',
      loadConfigSuccess: 'Configuration loaded successfully',
      loadConfigFailed: 'Failed to load configuration, please refresh and try again',
      saveFailed: 'Save failed, please try again',
      resetFailed: 'Reset failed, please try again',
      importFailed: 'Import failed, please check file format',
      groupDisplayNames: {
        manus: 'Manus',
        browser: 'Browser',
        interaction: 'Interaction',
        system: 'System',
        performance: 'Performance',
      },
      restoreAllDefaults: 'Restore to Default',
      restoreAllDefaultsConfirm: 'Are you sure you want to restore all configurations to default values? This will overwrite all custom configurations.',
      restoreAllDefaultsSuccess: 'All configurations have been restored to default values',
      restoreAllDefaultsFailed: 'Failed to restore default configurations',
    },
    promptConfig: {
      title: 'Dynamic Prompt Configuration',
      configuredprompts: 'Configured Prompts',
      loadDetailsFailed: 'Failed to load prompt details',
      promptCount: 'prompt(s)',
      noPrompts: 'No prompts configured',
      createNew: 'Create New Prompt',
      promptName: 'Prompt Name',
      placeholder: 'Please enter',
      promptContent: 'Prompt Content',
      messageType: 'Message Type',
      type: 'Category Type',
      builtIn: 'Built-in',
      custom: 'Custom',
      namespace: 'Namespace',
      promptNamePlaceholder: 'Enter prompt name',
      selectPromptHint: 'Please select a prompt to configure',
      promptContentPlaceholder: 'Enter prompt content',
      descriptionPlaceholder: 'Describe the function and purpose of this prompt',
      description: 'Description',
      requiredFields: 'Please fill in all required fields',
      newPrompt: 'New Dynamic Prompt',
      saveSuccess: 'Prompt saved successfully',
      saveFailed: 'Failed to save prompt',
      deleteSuccess: 'Prompt deleted successfully',
      deleteFailed: 'Failed to delete prompt',
      deleteConfirm: 'Delete Confirmation',
      deleteConfirmText: 'Are you sure you want to delete',
      deleteWarning: 'This action cannot be undone.',
      exportSuccess: 'Configuration exported successfully',
      exportFailed: 'Failed to export configuration',
      importSuccess: 'Configuration imported successfully',
      importFailed: 'Failed to import configuration',
      resetToLanguageDefault: 'Reset to Language Default',
      selectLanguage: 'Select Language',
      resetToLanguageDefaultSuccess: 'Reset to language default successfully',
      resetToLanguageDefaultFailed: 'Failed to reset to language default',
      resetLanguageWarning: 'This will overwrite current content with the default version of selected language',
      batchSwitchLanguage: 'Batch Switch Language',
      batchSwitchLanguageSuccess: 'Batch language switch successful',
      batchSwitchLanguageFailed: 'Failed to batch switch language',
      batchSwitchLanguageWarning: 'This will overwrite all prompt content and descriptions with the default version of selected language',
    },
    namespaceConfig: {
      title: 'Namespace Configuration',
      name: 'Namespace Name',
      code: 'Namespace Code',
      host: 'Host',
      description: 'Namespace Description',
      loadDetailsFailed: 'Failed to load namespace details',
      selectNameSpaceHint: 'Please select a namespace to configure',
      createNew: 'Create New Namespace',
      placeholder: 'Please enter',
      saveSuccess: 'Saved successfully',
      saveFailed: 'Save failed',
      deleteSuccess: 'Deleted successfully',
      deleteFailed: 'Delete failed',
      deleteConfirm: 'Delete Confirmation',
      deleteConfirmText: 'Are you sure you want to delete',
      deleteWarning: 'This action cannot be undone.',
      configured: 'Configured Namespaces',
      namespace: {
        selectNamespace: 'Please select a namespace',
        namespace: 'Namespace',
      },
    },
  },

  // Agent configuration
  agent: {
    title: 'Agent Configuration',
    name: 'Agent Name',
    description: 'Description',
    prompt: 'Agent Prompt (personality, requirements, and next step guidance)',
    tools: 'Tools',
    addAgent: 'Add Agent',
    editAgent: 'Edit Agent',
    deleteAgent: 'Delete Agent',
    deleteConfirm: 'Are you sure you want to delete?',
    deleteWarning: 'This action cannot be undone.',
    namePlaceholder: 'Enter agent name',
    descriptionPlaceholder: 'Describe the function and purpose of this agent',
    promptPlaceholder: "Set the agent's personality, requirements, and next step guidance...",
    toolSelection: 'Tool Selection',
    availableTools: 'Available Tools',
    selectedTools: 'Selected Tools',
    selectTools: 'Select Tools',
    required: '*',
    saveSuccess: 'Agent saved successfully',
    saveFailed: 'Failed to save agent',
    deleteSuccess: 'Agent deleted successfully',
    deleteFailed: 'Failed to delete agent',
    // Multi-language support
    multiLanguage: {
      title: 'Agent Multi-Language Management',
      resetAll: 'Reset All Agents',
      resetAllConfirm: 'Reset All Agents Confirmation',
      resetAllWarning: 'This operation will delete all existing agent configurations and reload the specified language version. This action cannot be undone!',
      selectLanguage: 'Select Language',
      resetSuccess: 'All agents have been reset to the specified language version',
      resetFailed: 'Failed to reset agents',
      currentLanguage: 'Current Language',
      supportedLanguages: 'Supported Languages',
      resetInProgress: 'Resetting agents...',
      confirmReset: 'Confirm Reset',
      cancel: 'Cancel',
    },
  },
  // Model Configuration
  model: {
    title: 'Model Configuration',
    switch: 'Switch Model',
    name: 'Model Name',
    description: 'Description',
    addModel: 'Add Model',
    editModel: 'Edit Model',
    deleteModel: 'Delete Model',
    deleteConfirm: 'Are you sure you want to delete?',
    deleteWarning: 'This action cannot be undone.',
    namePlaceholder: 'Enter model name',
    descriptionPlaceholder: 'Model Description',
    required: '*',
    saveSuccess: 'Model saved successfully',
    saveFailed: 'Failed to save model',
    deleteSuccess: 'Model deleted successfully',
    deleteFailed: 'Failed to delete model',
  },
  // Model configuration page
  modelConfig: {
    title: 'Model Configuration',
    import: 'Import',
    export: 'Export',
    configuredModels: 'Configured Models',
    modelCount: 'models',
    noModel: 'No model configurations available',
    createNew: 'Create New Model',
    selectModelHint: 'Please select a model to configure',
    newModel: 'New Model',
    modelName: 'Model Name',
    modelNamePlaceholder: 'Enter model name',
    description: 'Description',
    descriptionPlaceholder: 'Describe the model and its use cases',
    deleteConfirm: 'Delete Confirmation',
    deleteConfirmText: 'Are you sure you want to delete',
    deleteWarning: 'This action cannot be undone.',
    requiredFields: 'Please fill in all required fields',
    createSuccess: 'Model created successfully',
    createFailed: 'Failed to create model',
    saveSuccess: 'Model saved successfully',
    saveFailed: 'Failed to save model',
    deleteSuccess: 'Model deleted successfully',
    deleteFailed: 'Failed to delete model',
    importSuccess: 'Model imported successfully',
    importFailed: 'Failed to import model',
    exportSuccess: 'Model exported successfully',
    exportFailed: 'Failed to export model',
    loadDataFailed: 'Failed to load data',
    loadDetailsFailed: 'Failed to load model details',
    invalidFormat: 'Model configuration format is invalid: missing required fields',
  },
  // Plan template configuration
  planTemplate: {
    title: 'Plan Template Configuration',
    generator: 'Plan Generator',
    execution: 'Plan Execution',
    prompt: 'Generation Prompt',
    promptPlaceholder: 'Describe the plan you want to generate...',
    generating: 'Generating...',
    generate: 'Generate Plan',
    updatePlan: 'Update Plan',
    executing: 'Executing...',
    execute: 'Execute Plan',
    executionParams: 'Execution Parameters',
    executionParamsPlaceholder: 'Enter execution parameters (optional)...',
    apiUrl: 'API Call URL',
    clearParams: 'Clear Parameters',
    versionControl: 'Version Control',
    rollback: 'Rollback',
    restore: 'Restore',
    currentVersion: 'Current Version',
    saveTemplate: 'Save Template',
    loadTemplate: 'Load Template',
    templateSaved: 'Template saved',
    templateLoaded: 'Template loaded',
    executionSuccess: 'Execution successful',
    executionFailed: 'Execution failed',
    generationSuccess: 'Generation successful',
    generationFailed: 'Generation failed',
    invalidJson: 'Invalid JSON format, please correct and save again',
    executePlanTemplate: 'Execute Plan Template',
  },

  // Chat component
  chat: {
    botName: 'JManus:',
    thinkingLabel: 'JManus Thinking/Processing',
    processing: 'Processing...',
    step: 'Step',
    stepNumber: 'Step {number}',
    stepExecutionDetails: 'Step Execution Details',
    status: {
      executing: 'Executing',
      completed: 'Completed',
      pending: 'Pending',
      failed: 'Failed',
      inProgress: 'In Progress',
    },
    userInput: {
      message: 'Please enter the required information:',
      submit: 'Submit',
    },
    thinking: 'Thinking...',
    thinkingAnalyzing: 'Analyzing task requirements...',
    thinkingExecuting: 'Executing: {title}',
    thinkingResponse: 'Organizing response for you...',
    planningExecution: 'Planning and executing your request...',
    copyResponse: 'Copy Response',
    regenerateResponse: 'Regenerate Response',
    thinkingProcessing: 'Processing your request...',
    preparingExecution: 'Preparing to execute plan...',
    preparing: 'Preparing...',
    response: 'Response',
    retry: 'Retry',
    regenerate: 'Regenerate',
    copy: 'Copy',
    scrollToBottom: 'Scroll to Bottom',
    waitingDecision: 'Waiting for Decision',
    executionCompleted: 'Execution Completed',
    noTool: 'No Tool',
    noToolParameters: 'No Tool Parameters',
    executionError: 'Execution Error',
    newMessage: 'New Message',
    networkError: 'Network connection issue, please check your network connection and try again',
    authError: 'Access permission issue, please contact administrator or try later',
    formatError: 'Request format might be incorrect, could you please rephrase your request?',
    unknownError: 'Encountered some issues while processing your request, please try again later',
    thinkingOutput: 'Thinking Output',
    defaultResponse: 'I understand. Is there anything else I can help you with?',
    anythingElse: 'Is there anything else I can help you with?',
    okayDone: 'Okay, {text}',
    ifOtherQuestions: 'If you have any other questions, please feel free to let me know',
    hopeHelpful: 'I hope this answer is helpful to you!',
    great: 'Great!',
    ifOtherHelp: 'If you need any other help, please feel free to let me know',
    completedRequest: 'I have completed your request: {result}',
    // Sub-plan execution related
    subPlanExecutions: 'Sub-Plan Executions',
    agentExecutions: 'Agent Executions',
    agentExecutionSequence: 'Agent Execution Sequence',
    progress: 'Progress',
    thinkActSteps: 'Think-Act Steps',
    unknownAgent: 'Unknown Agent',
    agentResult: 'Agent Result',
    errorMessage: 'Error Message',
    triggeredByTool: 'Triggered by Tool',
    planExecution: 'Plan Execution',
    userInputRequired: 'User Input Required'
  },

  // Input component
  input: {
    placeholder: 'Send a message to JManus',
    send: 'Send',
    planMode: 'PLAN-ACT Template Mode',
    waiting: 'Waiting for user input...',
    maxLength: 'Max Length',
    charactersRemaining: 'Characters Remaining',
    attachFile: 'Attach File',
    attachedFiles: 'Attached Files',
    filesAttached: '{count} files attached',
    removeFile: 'Remove file',
    uploading: 'Uploading...',
    uploadSuccess: 'Files uploaded successfully',
    fileDeleteError: 'Failed to delete file',
    uploadError: 'File upload failed',
    unsupportedFileType: 'Unsupported file type',
    fileSizeExceeded: 'File size exceeded limit',
    maxFileSize: 'Max file size: 50MB',
    supportedFormats: 'Supported formats: PDF, text, CSV, JSON, XML, HTML, logs, code files, etc.',
  },

  // Sidebar
  sidebar: {
    title: 'PLAN-ACT Template Name',
    templateList: 'Template List',
    configuration: 'Configuration',
    newPlan: 'New Plan',
    loading: 'Loading...',
    retry: 'Retry',
    noTemplates: 'No available plan templates',
    unnamedPlan: 'Unnamed Plan',
    noDescription: 'No description',
    deleteTemplate: 'Delete this plan template',
  jsonTemplate: 'plan-act计划模板编辑器',
    rollback: 'Rollback',
    restore: 'Restore',
    jsonPlaceholder:
      'Step 2: Here you can directly modify the execution plan generated in Step 1 to make it more accurately follow your intentions. Then you can click Execute Plan to run this plan with high certainty.',
    planGenerator: 'Plan Generator',
    generatorPlaceholder:
      'Step 1: Enter the task you want to accomplish here in natural language, as detailed as possible. Then click Generate Plan to create a precise, repeatable plan.',
    generating: 'Generating...',
    generatePlan: 'Generate Plan',
    updatePlan: 'Update Plan',
    executionController: 'Execution Controller',
    executionParams: 'Execution Parameters',
    executionParamsPlaceholder: 'Enter execution parameters...',
    executionParamsHelp:
      'When executing repeatedly, you can set some content in Step 2 as variables, then specify the specific values for those variables here. For example, set "Variable1" in the JSON, then set "Variable1=Alibaba" here to achieve function parameter-like effects.',
    clearParams: 'Clear Parameters',
    parameterRequirements: 'Parameter Requirements',
    parameterRequirementsHelp: 'You can embed variable parameters in the "task requirements section" using <<parameter_name>> format. After saving, these variable parameters can be used in the subsequent service publishing.',
    clearAllParams: 'Clear All Parameters',
    noParametersRequired: 'This plan template does not require any parameters',
    fillAllRequiredParameters: 'Please fill in all required parameters',
    apiUrl: 'HTTP GET URL',
    statusApiUrl: 'Status Query API',
    executing: 'Executing...',
    executePlan: 'Execute Plan',
    publishMcpService: '发布为工具服务',
    updateServiceStatus: '更新服务状态',
    newTemplate: 'New Template',
    templateName: 'Template Name',
    templateDescription: 'Template Description',
    planTemplateId: 'Plan Template ID',
    planTemplateIdPlaceholder: 'Template ID will be generated automatically',
    lastModified: 'Last Modified',
    createTime: 'Create Time',
    expand: 'Expand',
    collapse: 'Collapse',
    pin: 'Pin',
    unpin: 'Unpin',
    favorite: 'Favorite',
    unfavorite: 'Unfavorite',
    share: 'Share',
    duplicate: 'Duplicate',
    rename: 'Rename',
    move: 'Move',
    archive: 'Archive',
    unarchive: 'Unarchive',
    selectTemplateFailed: 'Failed to select plan template',
    confirmDelete:
      'Are you sure you want to delete plan template "{name}"? This action cannot be undone.',
    templateDeleted: 'Plan template has been deleted.',
    deleteTemplateFailed: 'Failed to delete plan template',
    saveCompleted: 'Save completed: {message}\n\nCurrent version count: {versionCount}',
    saveSuccess: 'Save successful: {message}\n\nCurrent version count: {versionCount}',
    saveStatus: 'Save status: {message}',
    saveFailed: 'Failed to save plan modifications',
    generateSuccess: 'Plan generated successfully! Template ID: {templateId}',
    generateFailed: 'Failed to generate plan',
    updateSuccess: 'Plan updated successfully!',
    updateFailed: 'Failed to update plan',
    executeFailed: 'Failed to execute plan',
    unknown: 'Unknown',
    newTemplateName: 'New Execution Plan',
    newTemplateDescription: 'Please use the plan generator to create a new plan template',
    generatedTemplateDescription: 'Plan template created by generator',
    defaultExecutionPlanTitle: 'Execution Plan',

    // JsonEditor related
  planId: 'Plan Template ID',
    steps: 'Steps',
  tasks: '任务列表',
    subtask: 'Subtask',
    agent: 'Agent',
    selectAgent: 'Select Agent',
  stepRequirement: 'Task Requirement',
  stepRequirementPlaceholder: 'Please enter the specific requirements and description for this task...',
  terminateColumns: 'Task Output Requirements Description',
  terminateColumnsPlaceholder: 'Specify structured return values. If specified, returns a JSON list where each row contains your specified columns. For example: col1,col2 will output [(col1:val1,col2:val2), (col1:val3,col2:val4)]',
    titlePlaceholder: 'Please enter plan title...',
    moveUp: 'Move Up',
    moveDown: 'Move Down',
    removeStep: 'Remove Step',
    addStep: 'Add Step',
    addFirstStep: 'Add First Step',
    noSteps: 'No steps yet, click the button below to start adding',
    jsonPreview: 'JSON Preview',
    showJson: 'Show JSON',
    hideJson: 'Hide JSON',
    refreshAgents: 'Refresh Agents',
    agentLoadError: 'Failed to load agents',
    retryLoadAgents: 'Retry loading agents',
    availableTools: 'Available Tools',
    
    // Plan Type Selection
    planType: 'Plan Type',
    simplePlan: 'Simple Plan',
    advancedPlan: 'Advanced Plan (MapReduce)',
    dynamicAgentPlan: 'Dynamic Agent Plan',
    dynamicAgentInstruction: 'Use dynamic agent planning, just input instructions directly without going through the generation process. Please build your plan directly in the dynamic agent planning section.',
    
    // Tool Selection
    selectedTools: 'Selected Tools',
    addTools: 'Add Tools',
    removeTool: 'Remove Tool',
  clearAllTools: 'Clear All Tools',
  noToolsSelected: 'No tools selected',
  planTypeError: 'Plan Type Error',
  
  // Model and Tool Keys
  modelName: 'Model Name',
  modelNameDescription: 'Specify model name for handling specific tasks (such as image recognition, etc.). If not specified, the default model will be used.',
  noModelSelected: 'Empty',
  noTools: 'No tools',
  addTool: 'Add Tool',
  toolPlaceholder: 'Enter tool',
  addRemoveTools: 'Add/Remove Tools',

    // Common error messages
    loadingFailed: 'Loading failed',
    networkError: 'Network error',
    apiError: 'API call failed',
    resizeHint: 'Drag to resize',
  },

  // Tool Selection
  toolSelection: {
    title: 'Select Tools',
    searchPlaceholder: 'Search tools...',
    sortByGroup: 'Sort by Service Group',
    sortByName: 'Sort by Name',
    sortByStatus: 'Sort by Status',
    summary: '{groups} service groups, {tools} tools ({selected} selected)',
    enableAll: 'Enable All',
    noToolsFound: 'No tools found'
  },

  // Direct execution page
  direct: {
    planTemplateIdNotFound: 'Plan template ID not found',
    executionFailedNoPlanId: 'Plan execution failed: No valid plan ID returned',
    executionFailed: 'Plan execution failed',
    configuration: 'config',
    panelResizeHint: 'Drag to resize panel',
  },

  // Modal
  modal: {
    close: 'Close',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
  },

  // Editor
  editor: {
    format: 'Format',
    undo: 'Undo',
    redo: 'Redo',
    find: 'Find',
    replace: 'Replace',
    gotoLine: 'Go to Line',
    selectAll: 'Select All',
    toggleWordWrap: 'Toggle Word Wrap',
    toggleMinimap: 'Toggle Minimap',
    increaseFontSize: 'Increase Font Size',
    decreaseFontSize: 'Decrease Font Size',
    resetFontSize: 'Reset Font Size',
  },

  // Validation
  validation: {
    required: 'Content cannot be empty',
  },

  // Theme
  theme: {
    switch: 'Switch Theme',
    light: 'Light Theme',
    dark: 'Dark Theme',
    auto: 'Follow System',
  },

  // Error pages
  error: {
    notFound: 'Page Not Found',
    notFoundDescription: 'Sorry, the page you are looking for does not exist',
    serverError: 'Server Error',
    serverErrorDescription: 'Server encountered some issues, please try again later',
    networkError: 'Network Error',
    networkErrorDescription: 'Network connection failed, please check your network settings',
    backToHome: 'Back to Home',
    retry: 'Retry',
  },

  // Time related
  time: {
    now: 'Just now',
    unknown: 'Unknown time',
    minuteAgo: '{count} minutes ago',
    hourAgo: '{count} hours ago',
    dayAgo: '{count} days ago',
    weekAgo: '{count} weeks ago',
    monthAgo: '{count} months ago',
    yearAgo: '{count} years ago',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    lastWeek: 'Last Week',
    nextWeek: 'Next Week',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    nextMonth: 'Next Month',
    thisYear: 'This Year',
    lastYear: 'Last Year',
    nextYear: 'Next Year',
  },

  // Data statistics
  stats: {
    total: 'Total',
    count: 'Count',
    percentage: 'Percentage',
    average: 'Average',
    median: 'Median',
    min: 'Minimum',
    max: 'Maximum',
    sum: 'Sum',
    growth: 'Growth',
    decline: 'Decline',
    noData: 'No data',
    loading: 'Loading data...',
    error: 'Failed to load data',
  },

  // Home page
  home: {
    welcomeTitle: 'Welcome to JManus!',
    welcomeSubtitle:
      'Your Java AI intelligent assistant, helping you build and complete various tasks.',
    tagline: 'Java AI Agent',
    inputPlaceholder: 'Describe what you want to build or accomplish...',
    directButton: 'Enter Workbench Directly',
    examples: {
      stockPrice: {
        title: 'Query Stock Price',
        description: "Get today's latest stock price for Alphabet (Agent can use browser tools)",
        prompt:
          "Use browser based on Google to query today's Alphabet stock price and return the latest stock price\n",
      },
      weather: {
        title: 'Query Weather',
        description: "Get today's weather in New York (Agent can use MCP tool services)",
        prompt: "Use browser, based on Google, to query today's weather in New York",
      },
      queryplan: {
        title: 'Query Person Info',
        description: "Query all information about Shenxun Ali (to demonstrate infinite context capability)",
        prompt: 'Use browser, based on Baidu, to query person info',
        planTitle: 'Query all information about Shenxun Ali (to demonstrate infinite context capability)',
        step1: '[BROWSER_AGENT] Search for Shenxun Ali through Baidu, get the first page HTML data, merge and aggregate to html_data directory',
        step1Output: 'Storage directory path',
        step2: '[TEXT_FILE_AGENT] Find all valid web links about Shenxun Ali from html_data directory, output to link.md',
        step2Output: 'URL address, description',
      },
      ainovel: {
        title: 'AI Novel Creation',
        description: 'AI gradually defeats humanity themed novel (to demonstrate long-form content output)',
        prompt: 'Create a novel about artificial intelligence gradually defeating humanity, including 10 chapters',
        planTitle: 'AI Gradually Defeats Humans Novel Creation Plan',
        step1: '[TEXT_FILE_AGENT] Create a file with novel title and chapter titles, expecting a novel with 10 chapters, output outline to novel.md, each chapter uses secondary title, only write chapter titles in current step, novel title is "AI Gradually Defeats Humans"',
        step1Output: 'File name',
        step2: '[TEXT_FILE_AGENT] Get chapter title information from novel.md file, then improve each chapter content in sequence, only improve one chapter content per round, use replace to update content, each chapter requires 3000 words, do not query all document content after updating each chapter',
        step2Output: 'File name',
      },
      formInputDemo: {
        title: 'AI Intelligent Dynamic Form Demo',
        description: 'Experience AI intelligent analysis of user needs and dynamic generation of relevant form fields',
        prompt: 'Demonstrate how AI intelligently generates targeted form fields based on user questions',
        planTitle: 'AI Intelligent Dynamic Form Demo Plan',
        step1: '[INTELLIGENT_FORM_AGENT] Ask user to input consultation type, there are three types: business consultation, technical consultation, and architecture consultation',
        step1Output: 'Business type',
        step2: '[DEFAULT_AGENT] If technical consultation, output: Technical consultation please call 10086\n' + 'If architecture consultation, output: Architecture consultation please contact Shenxun\n' + 'Otherwise output empty, not within my scope',
        step2Output: '',
        step3: '[DEFAULT_AGENT] If business consultation, output: Business consultation, you can submit through official website www.yida.com',
        step3Output: '',
      },
    },
  },

  // Right panel
  rightPanel: {
    stepExecutionDetails: 'Step Execution Details',
    noStepSelected: 'No Step Selected',
    selectStepHint: 'Please select an execution step in the left chat area to view details',
    stepExecuting: 'Step is executing, please wait...',
    step: 'Step',
    executingAgent: 'Executing Agent',
    callingModel: 'Calling Model',
    executionResult: 'Execution Result',
    executing: 'Executing...',
    thinkAndActionSteps: 'Think & Action Steps',
    thinking: 'Thinking',
    action: 'Action',
    input: 'Input',
    output: 'Output',
    tool: 'Tool',
    toolParameters: 'Tool Parameters',
    noStepDetails: 'No detailed step information available',
    scrollToBottom: 'Scroll to Bottom',
    stepInfo: 'Step Information',
    stepName: 'Step Name',
    noExecutionInfo: 'No detailed execution information available for this step',
    subPlan: 'Sub Execution Plan',
    subStep: 'Sub Step',
    subPlanId: 'Sub Plan ID',
    title: 'Title',
    planTemplateId: 'Plan Template ID',
    planTemplateIdPlaceholder: 'Plan template identifier',
    stepNumber: 'Step {number}',
    status: {
      label: 'Status',
      completed: 'Completed',
      executing: 'Executing',
      pending: 'Pending',
      inProgress: 'In Progress',
      unknown: 'Unknown'
    },
    // Nested sub-plan related
    nestedSubPlans: 'Nested Sub-Plans',
    directSubPlans: 'Direct Sub-Plans',
    andMoreSteps: 'and {count} more steps',
    // Tab labels
    tabs: {
      details: 'Step Execution Details',
      chat: 'Chat',
      code: 'Code',
    },
    // Sample chatBubbles data
    chatBubbles: {
      analyzeRequirements: {
        title: 'Analyze Requirements',
        content:
          'Breaking down your request into actionable steps: 1) Create user entity, 2) Implement user service, 3) Build REST endpoints, 4) Add validation and error handling.',
      },
      generateCode: {
        title: 'Generate Code',
        content:
          'Creating Spring Boot REST API with user management CRUD operations. Including proper HTTP status codes and error handling.',
      },
      codeGenerated: {
        title: 'Code Generated',
        content:
          'Successfully generated UserController with all CRUD operations. The code includes proper REST conventions, error handling, and follows Spring Boot best practices.',
      },
    },
    // Time display
    timeAgo: {
      justNow: 'Just now',
      minutesAgo: '{n} minutes ago',
      hoursAgo: '{n} hours ago',
      daysAgo: '{n} days ago',
    },
    // Default step title
    defaultStepTitle: 'Step {number}',
  },

  // Cron Task
  cronTask: {
    title: 'Cron Task Management',
    addTask: 'Cron Task',
    noTasks: 'No cron tasks',
    taskName: 'Task Name',
    taskNamePlaceholder: 'Please enter task name',
    cronExpression: 'Cron Expression',
    cronExpressionPlaceholder: 'e.g: 0 0 12 * * ?',
    cronExpressionHelp: 'Format: second minute hour day month week year',
    taskDescription: 'Task Description',
    taskDescriptionPlaceholder: 'Please enter task description',
    taskStatus: 'Task Status',
    taskDetail: 'Task Detail',
    executeOnce: 'Execute Once',
    edit: 'Edit',
    operations: 'Operations',
    enable: 'Enable',
    disable: 'Disable',
    delete: 'Delete',
    deleteConfirm: 'Confirm Delete',
    deleteConfirmMessage:
      'Are you sure you want to delete task "{taskName}"? This action cannot be undone.',
    nextExecution: 'Next Execution Time',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    active: 'Active',
    inactive: 'Inactive',
    template: 'Example: Help me collect today\'s AI news every day at 8 AM',
    planTemplate: 'Plan Template',
    linkTemplate: 'Link Template',
    noTemplate: 'No Template',
    selectTemplate: 'Select Template',
    templateHelpText: 'After selection, the cron task will execute according to the defined plan',
    createTask: 'Create Cron Task',
    selectCreateMethod: 'Please select creation method',
    createWithJmanus: 'Create with Jmanus',
    createWithJmanusDesc: 'Create cron task with AI assistant guidance',
    createManually: 'Create Manually',
    createManuallyDesc: 'Fill in task information yourself',
  },

  // MCP Service
  mcpService: {
    title: 'MCP Service',
    createService: 'Create MCP Service',
    updateService: 'Update MCP Service',
    endpoint: 'Endpoint',
    endpointRequired: 'Endpoint *',
    endpointPlaceholder: 'Please select or enter endpoint',
    endpointDescription: 'Endpoint is required for MCP service connection, e.g. http://localhost:port/[endpoint]',
    httpEndpointRequired: 'HTTP Endpoint *',
    httpEndpointPlaceholder: 'Please enter HTTP endpoint address',
    httpEndpointDescription: 'HTTP POST service endpoint address for calling this function via HTTP interface',
    copy: 'Copy',
    copyFailed: 'Copy failed',
    selectEndpoint: 'Select Endpoint',
    manualInput: 'Manual Input Endpoint',
    addManualEndpoint: 'Add',
    mcpStreamableUrl: 'MCP Streamable URL',
    mcpStreamableUrlPlaceholder: 'Please enter endpoint to generate URL',
    copyUrl: 'Double-click to copy',
    toolName: 'Tool Name',
    toolNameRequired: 'Tool Name *',
    toolNamePlaceholder: 'Please enter tool name',
    toolNameDescription: 'Tell the model what this tool is called, also for your identification and modification',
    toolDescription: 'Tool Description',
    toolDescriptionRequired: 'Tool Description *',
    toolDescriptionPlaceholder: 'Please enter tool description',
    toolDescriptionDescription: 'Tell the model when to use this tool. The more detailed you write (when to choose, when not to choose), the more accurate the model will be in selecting tools.',
    serviceGroup: 'Service Group *',
    serviceGroupPlaceholder: 'Please enter service group',
    serviceGroupDescription: 'Used to group tools, making it easier for you to find your tool among many tools.',
    parameterConfig: 'Parameter Configuration',
    parameterConfigDescription: 'Use {placeholder} to add parameters and descriptions in the original execution content',
    parameterName: 'Parameter Name',
    parameterDescription: 'Parameter Description (Tell the model how to fill this parameter to meet your requirements, the more detailed with examples the better)',
    addParameter: 'Add Parameter',
    removeParameter: 'Remove Parameter',
    save: 'Save',
    saving: 'Saving...',
    publish: 'Publish',
    publishAsService: 'Publish as Service',
    publishAsMcpService: 'Also publish as MCP service',
    publishAsMcpServiceDescription: 'When checked, this will be published as an MCP service that others can connect to via endpoint',
    publishAsHttpService: 'Publish as HTTP service',
    publishAsHttpServiceDescription: 'When checked, this will be published as an HTTP POST service that can be called via HTTP interface',
    publishAsInternalToolcall: 'Publish as Internal Toolcall',
    publishAsInternalToolcallDescription: 'When checked, this will be published as an internal tool that can be used by agents in their tool configuration',
    publishing: 'Publishing...',
    delete: 'Delete',
    deleting: 'Deleting...',
    published: 'Published',
    unpublished: 'Unpublished',
    saveSuccess: 'MCP service saved successfully',
    saveFailed: 'Failed to save MCP service',
    publishSuccess: 'MCP service published successfully',
    httpPublishSuccess: 'HTTP service published successfully',
    publishFailed: 'Failed to publish MCP service',
    deleteSuccess: 'MCP service deleted successfully',
    deleteFailed: 'Failed to delete MCP service',
    deleteConfirm: 'Confirm Delete',
    deleteConfirmMessage: 'Are you sure you want to delete this MCP service? This action cannot be undone.',
    loadEndpointsFailed: 'Failed to load endpoints',
    loadToolDataFailed: 'Failed to load tool data',
    parseInputSchemaFailed: 'Failed to parse input schema',
    noPlanTemplateId: 'No plan template ID, skipping coordinator tool data loading',
    selectPlanTemplateFirst: 'Please select a plan template first',
    validationFailed: 'Form validation failed',
    endpointRequiredError: 'Endpoint is required',
    httpEndpointRequiredError: 'HTTP endpoint is required',
    toolNameRequiredError: 'Tool name is required',
    toolDescriptionRequiredError: 'Tool description is required',
  },

  memory: {
    title: 'Memory Management',
    searchPlaceholder: 'Search Memory',
    size: 'memories',
    newNamePlaceholder: 'Enter a new name...',
    changeName: 'Change Name',
    save: 'save',
    cancel: 'cancel',
    delete: 'delete',
    deleteHint: 'delete hint',
    deleteHintPrefix: 'Are you sure you want to delete the memory with ID',
    deleteHintSuffix: '? This action cannot be undone.',
    selectMemory: 'Select Memory',
    clearMemory: 'Clear Memory',
    newChat: 'new chat',
  },
  // Common placeholders
  selectCommon: {
    pleaseSelect: 'Please select...',
  },
}

export default words
