/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.manus.tool.pptGenerator;

import com.alibaba.cloud.ai.manus.tool.code.ToolExecuteResult;
import com.alibaba.cloud.ai.manus.tool.filesystem.UnifiedDirectoryManager;
import com.alibaba.cloud.ai.manus.tool.ToolPromptManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.JsonNode;

import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Iterator;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simplified PPT generator integration test
 */
public class PptGeneratorIntegrationTest {

	private static final Logger log = LoggerFactory.getLogger(PptGeneratorIntegrationTest.class);

	private PptGeneratorService pptGeneratorService;

	private ObjectMapper objectMapper;

	private ToolPromptManager toolPromptManager;

	private UnifiedDirectoryManager unifiedDirectoryManager;

	private PptGeneratorOperator pptGeneratorOperator;

	// @TempDir
	Path tempDir;

	@BeforeEach
	void setUp() {
		log.info("===== Starting test preparation =====");
		pptGeneratorService = new PptGeneratorService();
		log.info("Initializing PptGeneratorService complete");
		objectMapper = new ObjectMapper();
		log.info("Initializing ObjectMapper complete");
		toolPromptManager = mock(ToolPromptManager.class);
		log.info("Initializing ToolPromptManager mock complete");
		unifiedDirectoryManager = mock(UnifiedDirectoryManager.class);
		log.info("Initializing UnifiedDirectoryManager mock complete");

		// Inject unifiedDirectoryManager using reflection
		try {
			Field field = PptGeneratorService.class.getDeclaredField("unifiedDirectoryManager");
			field.setAccessible(true);
			field.set(pptGeneratorService, unifiedDirectoryManager);
			log.info("Successfully injected UnifiedDirectoryManager into PptGeneratorService");
		}
		catch (Exception e) {
			log.error("Failed to inject unifiedDirectoryManager", e);
			throw new RuntimeException("Failed to inject unifiedDirectoryManager", e);
		}

		pptGeneratorOperator = new PptGeneratorOperator(pptGeneratorService, objectMapper, unifiedDirectoryManager);
		log.info("Initializing PptGeneratorOperator complete");

		// Set tempDir to a real path
		tempDir = Path.of("./");
		log.info("Setting test output directory: {}", tempDir);

		// Setup mock behavior
		when(toolPromptManager.getToolDescription("pptGenerator")).thenReturn("PPT Generator Tool");
		when(toolPromptManager.getToolParameters("pptGenerator")).thenReturn("PPT parameters");
		log.info("Setting ToolPromptManager mock behavior complete");

		// Simplify path validation for testing
		try {
			when(unifiedDirectoryManager.getSpecifiedDirectory(anyString())).thenAnswer(invocation -> {
				String path = invocation.getArgument(0);
				return tempDir.resolve(path).toAbsolutePath();
			});
			log.info("Setting UnifiedDirectoryManager mock behavior complete");
		}
		catch (Exception e) {
			log.error("Error setting up UnifiedDirectoryManager mock", e);
			throw new RuntimeException(e);
		}
		log.info("===== Test preparation complete =====");
	}

	@Test
	void testCreateBasicPptSuccessfully() throws Exception {
		log.info("\n===== Starting test: Create PPT with basic content =====");
		// Prepare test data
		String planId = "test-plan-123";
		String fileName = "test-0.pptx";
		log.info("Preparing test data: planId={}, fileName={}", planId, fileName);

		PptInput input = new PptInput();
		input.setAction("create");
		input.setFileName(fileName);
		input.setTitle("Test Presentation");
		input.setSubtitle("Generated by Test");
		log.info("Setting PPT basic information complete: title={}, subtitle={}", input.getTitle(),
				input.getSubtitle());

		// Create simple slide content
		PptInput.SlideContent slide = new PptInput.SlideContent();
		slide.setTitle("First Slide");
		slide.setContent("This is content for the first slide");
		input.setSlideContents(Collections.singletonList(slide));
		log.info("Slide content setting completed: A total of {} slides", input.getSlideContents().size());

		// Set current plan ID
		pptGeneratorOperator.setCurrentPlanId(planId);
		log.info("Setting current plan ID: {}", planId);

		// Execute test
		log.info("Starting PPT generation operation...");
		ToolExecuteResult result = pptGeneratorOperator.run(input);
		log.info("PPT generation operation completed");

		// Verify results
		log.info("Starting result verification...");
		assertNotNull(result);
		log.info("Verification result is not null: success");
		assertTrue(result.getOutput().contains("success"));
		log.info("Verification result contains success information: success");
		assertTrue(result.getOutput().contains(fileName));
		log.info("Verification result contains file name: success");

		// Restore file creation verification
		Path pptPath = tempDir.resolve("extensions/pptGenerator/" + fileName);
		log.info("Verification file creation: {}", pptPath);
		assertTrue(Files.exists(pptPath));
		log.info("Verification file exists: success");
		assertTrue(Files.size(pptPath) > 0);
		log.info("Verification file size greater than 0: success");

		log.info("===== Test complete: Create PPT with basic content =====");
	}

	@Test
	void testCreatePptWithMinimalInput() throws Exception {
		log.info("\n===== Starting test: Create PPT with minimal input =====");
		// Prepare test data with minimal required fields
		String planId = "minimal-test";
		String fileName = "minimal-0.pptx";
		log.info("Preparing test data: planId={}, fileName={}", planId, fileName);

		PptInput input = new PptInput();
		input.setAction("create");
		input.setFileName(fileName);
		input.setTitle("Minimal PPT");
		log.info("Setting minimal PPT information complete: title={}", input.getTitle());

		// No slides content
		input.setSlideContents(null);
		log.info("Setting no slide content: success");

		// Set current plan ID
		pptGeneratorOperator.setCurrentPlanId(planId);
		log.info("Setting current plan ID: {}", planId);

		// Execute test
		log.info("Starting minimal PPT generation operation...");
		ToolExecuteResult result = pptGeneratorOperator.run(input);
		log.info("Minimal PPT generation operation completed");

		// Verify results
		log.info("Starting result verification...");
		assertNotNull(result);
		log.info("Verification result is not null: success");
		assertTrue(result.getOutput().contains("success"));
		log.info("Verification result contains success information: success");

		// Restore file creation verification
		Path pptPath = tempDir.resolve("extensions/pptGenerator/" + fileName);
		log.info("Verification file creation: {}", pptPath);
		assertTrue(Files.exists(pptPath));
		log.info("Verification file exists: success");

		log.info("===== Test complete: Create PPT with minimal input =====");
	}

	@Test
	void testApplyTemplate() throws Exception {
		log.info("\n===== Starting test: Apply template test =====");
		// Prepare test data
		String planId = "template-list-test";
		log.info("Preparing test data: planId={}", planId);

		PptInput input = new PptInput();
		input.setAction("getTemplateList");
		log.info("Setting action to getTemplateList");

		// Set current plan ID
		pptGeneratorOperator.setCurrentPlanId(planId);
		log.info("Setting current plan ID: {}", planId);

		// Execute test
		log.info("Starting getTemplateList operation...");
		ToolExecuteResult result = pptGeneratorOperator.run(input);
		log.info("getTemplateList operation completed");

		// Verify results
		log.info("Starting result verification...");
		assertNotNull(result);
		log.info("Verification result is not null: success");
		// Verify that the return result is a valid JSON format
		JsonNode templateListJson = null;
		try {
			templateListJson = objectMapper.readTree(result.getOutput());
			log.info("Verification result is valid JSON format: success");
		}
		catch (Exception e) {
			log.error("Verification result is not valid JSON format: {}", e.getMessage());
			fail("Verification result is not valid JSON format: " + e.getMessage());
		}

		// Add test for the getTemplate operation
		log.info("Starting getTemplate operation test...");

		// Verify that the return result is a valid JSON format
		String firstTemplatePath = null;
		if (templateListJson != null && templateListJson.isObject()) {
			Iterator<Map.Entry<String, JsonNode>> fields = templateListJson.fields();
			if (fields.hasNext()) {
				Map.Entry<String, JsonNode> firstEntry = fields.next();
				JsonNode pathNode = firstEntry.getValue().get("path");
				if (pathNode != null && pathNode.isTextual()) {
					firstTemplatePath = pathNode.asText();
					log.info("Verification result: first template path is {}", firstTemplatePath);
				}
			}
		}

		// If a template path is successfully retrieved, test the getTemplate operation
		if (firstTemplatePath != null) {
			PptInput templateInput = new PptInput();
			templateInput.setAction("getTemplate");
			templateInput.setPath(firstTemplatePath);
			log.info("Setting getTemplate operation, path: {}", firstTemplatePath);

			// Set current plan ID
			pptGeneratorOperator.setCurrentPlanId(planId);
			log.info("Setting current plan ID: {}", planId);

			// Execute getTemplate test
			log.info("Starting getTemplate operation test...");
			ToolExecuteResult templateResult = pptGeneratorOperator.run(templateInput);
			log.info("getTemplate operation test completed");

			// Verify results
			log.info("Starting result verification...");
			assertNotNull(templateResult);
			log.info("Verification result is not null: success");
			// Verify that the return result is a valid JSON format
			JsonNode templateJson = null;
			try {
				templateJson = objectMapper.readTree(templateResult.getOutput());
				log.info("Verification result is valid JSON format: success");
				log.info("Verification result before modification: {}", templateJson.toPrettyString());
			}
			catch (Exception e) {
				log.error("Verification result is not valid JSON format: {}", e.getMessage());
				fail("Verification result is not valid JSON format: " + e.getMessage());
			}

			// Modify template content, change all content elements text to Test
			if (templateJson != null && templateJson.has("slides")) {
				JsonNode slidesNode = templateJson.get("slides");
				if (slidesNode.isArray() && slidesNode.size() > 0) {
					JsonNode firstSlide = slidesNode.get(0);
					if (firstSlide.has("content") && firstSlide.get("content").isArray()) {
						ArrayNode contentArray = (ArrayNode) firstSlide.get("content");
						for (JsonNode contentItem : contentArray) {
							if (contentItem.has("text")) {
								((ObjectNode) contentItem).put("text", "Test");
							}
						}
					}
				}
			}

			// Create a new PPT using the modified template content
			assertNotNull(templateJson);
			log.info("Verification result after modification: {}", templateJson.toPrettyString());
			String modifiedTemplateContent = objectMapper.writeValueAsString(templateJson);

			PptInput createInput = new PptInput();
			createInput.setPath(firstTemplatePath);
			createInput.setAction("create");
			String fileName = "applied-template-0.pptx";
			createInput.setFileName(fileName);
			createInput.setTemplateContent(modifiedTemplateContent);
			log.info("Setting create PPT operation, using modified template content");

			// Set current plan ID
			pptGeneratorOperator.setCurrentPlanId(planId);
			log.info("Setting current plan ID: {}", planId);

			// Execute create with template test
			log.info("Starting create PPT operation with template...");
			ToolExecuteResult createResult = pptGeneratorOperator.run(createInput);
			log.info("Create PPT operation with template completed");

			// Verify results
			log.info("Starting result verification...");
			assertNotNull(createResult);
			log.info("Verification result is not null: success");
			assertTrue(createResult.getOutput().contains("success"));
			log.info("Verification result contains success information: success");

			// Restore file creation verification
			Path pptPath = tempDir.resolve("extensions/pptGenerator/" + fileName);
			log.info("Verifying file creation: {}", pptPath);
			assertTrue(Files.exists(pptPath));
			log.info("Verification file exists: success");

			log.info("===== getTemplate operation test completed =====");
		}
		else {
			log.info("Verification result: no template file found, skipping getTemplate operation test");
		}

		log.info("===== Test completed: template application test =====");
	}

}
