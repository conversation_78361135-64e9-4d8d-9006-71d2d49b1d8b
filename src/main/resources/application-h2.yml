spring:
  datasource:
    url: jdbc:h2:file:./h2-data/openmanus_db;MODE=MYSQL;DATABASE_TO_LOWER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: $FSD#@!@#!#$!12341234
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  # JPA configuration
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
    show-sql: false
  ai:
    memory:
      h2:
        enabled: true
