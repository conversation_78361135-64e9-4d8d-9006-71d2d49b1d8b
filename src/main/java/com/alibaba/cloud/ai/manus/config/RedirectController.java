/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.manus.config;

import com.alibaba.cloud.ai.manus.config.startUp.AppStartupListener;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @time 2025/7/31
 * @desc jmanus homepage redirect controller
 */
@Controller
public class RedirectController {

	@RequestMapping("/")
	public String redirect() {
		return AppStartupListener.INIT_WEB_PATH;
	}

}
