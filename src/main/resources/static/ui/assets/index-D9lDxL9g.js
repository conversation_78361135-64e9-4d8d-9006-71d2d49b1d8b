import{d as z,u as E,r as m,c as F,o as B,L as O,a as p,b as n,e,f as v,g as k,h as f,i as g,t as o,n as $,w as r,v as N,j,k as S,l as M,m as K,p as D,T as P,F as H,q as J,s as G,x as W}from"./index-CoxzeSM8.js";import{I as b,_ as Q}from"./_plugin-vue_export-helper-Pwn8nLHl.js";import{L as X}from"./llm-check-BVkAKrj3.js";const Y={class:"init-container"},Z={class:"init-card"},x={class:"init-header"},ee={class:"logo"},se={class:"description"},ae={class:"step-indicator"},le={class:"step-label"},te={class:"step-label"},oe={key:0,class:"init-form language-selection"},ie={class:"form-group"},ne={class:"form-label"},de={class:"language-options"},ue={class:"language-content"},re={class:"language-flag"},ce={class:"language-text"},pe={class:"language-content"},me={class:"language-flag"},ve={class:"form-actions single"},fe=["disabled"],ge={key:1,class:"init-form"},be={class:"form-group"},he={class:"form-label"},ye={class:"config-mode-selection"},_e={class:"radio-text"},$e={class:"radio-text"},ke={key:0,class:"form-group"},Ne={for:"apiKey",class:"form-label"},Me={class:"api-key-input-container"},Ke=["type","placeholder","disabled"],we=["title"],Ue={class:"form-hint"},Ce={href:"https://bailian.console.aliyun.com/?tab=model#/api-key",target:"_blank",class:"help-link"},Le={key:1,class:"custom-config-section"},Se={class:"form-group"},De={for:"baseUrl",class:"form-label"},Pe=["placeholder","disabled"],Ae={class:"form-hint"},Ve={class:"form-group"},qe={for:"customApiKey",class:"form-label"},Te={class:"api-key-input-container"},Ie=["type","placeholder","disabled"],Re=["title"],ze={class:"form-group"},Ee={for:"modelName",class:"form-label"},Fe=["placeholder","disabled"],Be={class:"form-hint"},Oe={class:"form-group"},je={for:"modelDisplayName",class:"form-label"},He=["placeholder","disabled"],Je={class:"form-group"},Ge={for:"completionsPath",class:"form-label"},We=["placeholder","disabled"],Qe={class:"form-actions"},Xe=["disabled"],Ye=["disabled"],Ze={key:0,class:"loading-spinner"},xe={key:0,class:"error-message"},es={key:0,class:"success-message"},ss={class:"background-animation"},as=z({__name:"index",setup(ls){const{t:h,locale:U}=E(),w=W(),d=m(1),u=m(U.value||"en"),l=m({configMode:"dashscope",apiKey:"",baseUrl:"",modelName:"",modelDisplayName:"",completionsPath:""}),i=m(!1),c=m(""),C=m(!1),y=m(!1),_=m(!1),A=F(()=>l.value.apiKey.trim()?l.value.configMode==="custom"?l.value.baseUrl.trim()&&l.value.modelName.trim():!0:!1),V=async()=>{if(u.value)try{i.value=!0,await G(u.value),d.value=2}catch(s){console.warn("Failed to switch language:",s),d.value=2}finally{i.value=!1}},q=()=>{d.value=1},L=()=>{l.value.apiKey="",l.value.baseUrl="",l.value.modelName="",l.value.modelDisplayName="",l.value.completionsPath="",c.value="",y.value=!1,_.value=!1},T=()=>{if(!l.value.apiKey.trim())return c.value=h("init.apiKeyRequired"),!1;if(l.value.configMode==="custom"){if(!l.value.baseUrl.trim())return c.value=h("init.baseUrlRequired"),!1;if(!l.value.modelName.trim())return c.value=h("init.modelNameRequired"),!1}return!0},I=async()=>{if(T())try{i.value=!0,c.value="";const s={configMode:l.value.configMode,apiKey:l.value.apiKey.trim()};l.value.configMode==="custom"&&(s.baseUrl=l.value.baseUrl.trim(),s.modelName=l.value.modelName.trim(),s.modelDisplayName=l.value.modelDisplayName.trim()||l.value.modelName.trim(),s.completionsPath=l.value.completionsPath.trim());const t=await(await fetch("/api/init/save",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();t.success?(C.value=!0,localStorage.setItem("hasInitialized","true"),localStorage.setItem("hasVisitedHome","true"),X.clearCache(),t.requiresRestart?setTimeout(()=>{confirm(h("init.restartRequired"))?window.location.reload():w.push("/home")},2e3):setTimeout(()=>{w.push("/home")},2e3)):c.value=t.error||h("init.saveFailed")}catch(s){console.error("Save config failed:",s),c.value=h("init.networkError")}finally{i.value=!1}},R=async()=>{try{const a=await(await fetch("/api/init/status")).json();a.success&&a.initialized&&(localStorage.setItem("hasInitialized","true"),w.push("/home"))}catch(s){console.error("Check init status failed:",s)}};return B(()=>{const s=localStorage.getItem(O);s&&(s==="zh"||s==="en")&&(u.value=s,U.value=s),R()}),(s,a)=>(n(),p("div",Y,[e("div",Z,[e("div",x,[e("div",ee,[e("h1",null,[k(g(b),{icon:"carbon:bot",class:"logo-icon"}),a[12]||(a[12]=f(" JManus"))])]),e("h2",null,o(d.value===1?s.$t("init.welcomeStep"):s.$t("init.welcome")),1),e("p",se,o(d.value===1?s.$t("init.languageStepDescription"):s.$t("init.description")),1)]),e("div",ae,[e("div",{class:$(["step",{active:d.value>=1,completed:d.value>1}])},[a[13]||(a[13]=e("span",{class:"step-number"},"1",-1)),e("span",le,o(s.$t("init.stepLanguage")),1)],2),a[15]||(a[15]=e("div",{class:"step-divider"},null,-1)),e("div",{class:$(["step",{active:d.value>=2,completed:d.value>2}])},[a[14]||(a[14]=e("span",{class:"step-number"},"2",-1)),e("span",te,o(s.$t("init.stepModel")),1)],2)]),d.value===1?(n(),p("div",oe,[e("div",ie,[e("label",ne,o(s.$t("init.selectLanguageLabel")),1),e("div",de,[e("label",{class:$(["language-option",{active:u.value==="zh"}])},[r(e("input",{type:"radio","onUpdate:modelValue":a[0]||(a[0]=t=>u.value=t),value:"zh"},null,512),[[N,u.value]]),e("span",ue,[e("span",re,[k(g(b),{icon:"circle-flags:cn"})]),e("span",ce,[e("strong",null,o(s.$t("language.zh")),1),e("small",null,o(s.$t("init.simplifiedChinese")),1)])])],2),e("label",{class:$(["language-option",{active:u.value==="en"}])},[r(e("input",{type:"radio","onUpdate:modelValue":a[1]||(a[1]=t=>u.value=t),value:"en"},null,512),[[N,u.value]]),e("span",pe,[e("span",me,[k(g(b),{icon:"circle-flags:us"})]),a[16]||(a[16]=e("span",{class:"language-text"},[e("strong",null,"English"),e("small",null,"English (US)")],-1))])],2)])]),e("div",ve,[e("button",{type:"button",class:"submit-btn",disabled:!u.value,onClick:V},o(s.$t("init.continueToModel")),9,fe)])])):v("",!0),d.value===2?(n(),p("div",ge,[e("form",{onSubmit:j(I,["prevent"])},[e("div",be,[e("label",he,o(s.$t("init.configModeLabel")),1),e("div",ye,[e("label",{class:$(["radio-option",{active:l.value.configMode==="dashscope"}])},[r(e("input",{type:"radio","onUpdate:modelValue":a[2]||(a[2]=t=>l.value.configMode=t),value:"dashscope",onChange:L},null,544),[[N,l.value.configMode]]),e("span",_e,[e("strong",null,o(s.$t("init.dashscopeMode")),1),e("small",null,o(s.$t("init.dashscopeModeDesc")),1)])],2),e("label",{class:$(["radio-option",{active:l.value.configMode==="custom"}])},[r(e("input",{type:"radio","onUpdate:modelValue":a[3]||(a[3]=t=>l.value.configMode=t),value:"custom",onChange:L},null,544),[[N,l.value.configMode]]),e("span",$e,[e("strong",null,o(s.$t("init.customMode")),1),e("small",null,o(s.$t("init.customModeDesc")),1)])],2)])]),l.value.configMode==="dashscope"?(n(),p("div",ke,[e("label",Ne,[f(o(s.$t("init.apiKeyLabel"))+" ",1),a[17]||(a[17]=e("span",{class:"required"},"*",-1))]),e("div",Me,[r(e("input",{id:"apiKey","onUpdate:modelValue":a[4]||(a[4]=t=>l.value.apiKey=t),type:y.value?"text":"password",class:"form-input",placeholder:s.$t("init.apiKeyPlaceholder"),disabled:i.value,required:""},null,8,Ke),[[S,l.value.apiKey]]),e("button",{type:"button",class:"api-key-toggle-btn",onClick:a[5]||(a[5]=t=>y.value=!y.value),title:y.value?s.$t("init.hideApiKey"):s.$t("init.showApiKey")},[y.value?(n(),M(g(b),{key:0,icon:"carbon:view"})):(n(),M(g(b),{key:1,icon:"carbon:view-off"}))],8,we)]),e("div",Ue,[f(o(s.$t("init.apiKeyHint"))+" ",1),e("a",Ce,o(s.$t("init.getApiKey")),1)])])):v("",!0),l.value.configMode==="custom"?(n(),p("div",Le,[e("div",Se,[e("label",De,[f(o(s.$t("init.baseUrlLabel"))+" ",1),a[18]||(a[18]=e("span",{class:"required"},"*",-1))]),r(e("input",{id:"baseUrl","onUpdate:modelValue":a[6]||(a[6]=t=>l.value.baseUrl=t),type:"url",class:"form-input",placeholder:s.$t("init.baseUrlPlaceholder"),disabled:i.value,required:""},null,8,Pe),[[K,l.value.baseUrl]]),e("div",Ae,o(s.$t("init.baseUrlHint")),1)]),e("div",Ve,[e("label",qe,[f(o(s.$t("init.customApiKeyLabel"))+" ",1),a[19]||(a[19]=e("span",{class:"required"},"*",-1))]),e("div",Te,[r(e("input",{id:"customApiKey","onUpdate:modelValue":a[7]||(a[7]=t=>l.value.apiKey=t),type:_.value?"text":"password",class:"form-input",placeholder:s.$t("init.customApiKeyPlaceholder"),disabled:i.value,required:""},null,8,Ie),[[S,l.value.apiKey]]),e("button",{type:"button",class:"api-key-toggle-btn",onClick:a[8]||(a[8]=t=>_.value=!_.value),title:_.value?s.$t("init.hideApiKey"):s.$t("init.showApiKey")},[_.value?(n(),M(g(b),{key:0,icon:"carbon:view"})):(n(),M(g(b),{key:1,icon:"carbon:view-off"}))],8,Re)])]),e("div",ze,[e("label",Ee,[f(o(s.$t("init.modelNameLabel"))+" ",1),a[20]||(a[20]=e("span",{class:"required"},"*",-1))]),r(e("input",{id:"modelName","onUpdate:modelValue":a[9]||(a[9]=t=>l.value.modelName=t),type:"text",class:"form-input",placeholder:s.$t("init.modelNamePlaceholder"),disabled:i.value,required:""},null,8,Fe),[[K,l.value.modelName]]),e("div",Be,o(s.$t("init.modelNameHint")),1)]),e("div",Oe,[e("label",je,o(s.$t("init.modelDisplayNameLabel")),1),r(e("input",{id:"modelDisplayName","onUpdate:modelValue":a[10]||(a[10]=t=>l.value.modelDisplayName=t),type:"text",class:"form-input",placeholder:s.$t("init.modelDisplayNamePlaceholder"),disabled:i.value},null,8,He),[[K,l.value.modelDisplayName]])]),e("div",Je,[e("label",Ge,o(s.$t("init.completionsPath")),1),r(e("input",{id:"modelDisplayName","onUpdate:modelValue":a[11]||(a[11]=t=>l.value.completionsPath=t),type:"text",class:"form-input",placeholder:s.$t("init.completionsPathPlaceholder"),disabled:i.value},null,8,We),[[K,l.value.completionsPath]])])])):v("",!0),e("div",Qe,[e("button",{type:"button",class:"back-btn",onClick:q,disabled:i.value},o(s.$t("init.back")),9,Xe),e("button",{type:"submit",class:"submit-btn",disabled:i.value||!A.value},[i.value?(n(),p("span",Ze)):v("",!0),f(" "+o(i.value?s.$t("init.saving"):s.$t("init.saveAndContinue")),1)],8,Ye)])],32)])):v("",!0),k(P,{name:"error-fade"},{default:D(()=>[c.value?(n(),p("div",xe,o(c.value),1)):v("",!0)]),_:1}),k(P,{name:"success-fade"},{default:D(()=>[C.value?(n(),p("div",es,o(s.$t("init.successMessage")),1)):v("",!0)]),_:1})]),e("div",ss,[(n(),p(H,null,J(6,t=>e("div",{class:"floating-shape",key:t})),64))]),a[21]||(a[21]=e("div",{class:"background-effects"},[e("div",{class:"gradient-orb orb-1"}),e("div",{class:"gradient-orb orb-2"}),e("div",{class:"gradient-orb orb-3"})],-1))]))}}),ns=Q(as,[["__scopeId","data-v-3b010228"]]);export{ns as default};
