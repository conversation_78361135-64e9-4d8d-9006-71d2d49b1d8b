var Ls=Object.defineProperty;var Bs=(o,e,s)=>e in o?Ls(o,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[e]=s;var ge=(o,e,s)=>Bs(o,typeof e!="symbol"?e+"":e,s);import{d as ue,u as Pe,c as de,r as N,A as st,B as ce,o as we,a as f,b as p,F as ae,g as w,f as L,p as Me,h as ne,i as r,t as c,e as t,w as se,m as re,n as oe,q as ie,C as it,D as jt,l as le,E as nt,G as ns,H as os,I as Te,y as ze,j as me,T as qe,J as Qe,K as as,z as ke,v as Os,M as ls,N as Ze,x as rs,O as qs,_ as js}from"./index-CoxzeSM8.js";import{I as P,_ as pe}from"./_plugin-vue_export-helper-Pwn8nLHl.js";import{s as H,P as et,u as is}from"./sidebar-B6YvzJr4.js";import{M as Vs,A as Hs,T as Gs,u as cs,a as zs}from"./useMessage-BURepjwS.js";import{L as Ws}from"./llm-check-BVkAKrj3.js";import{L as Js}from"./LanguageSwitcher-XuP7-_TV.js";class De{static async getCoordinatorToolConfig(){try{const e=await fetch(`${this.BASE_URL}/config`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`Failed to get coordinator tool config: ${e.status}`);return await e.json()}catch(e){return console.error("Failed to get CoordinatorTool configuration:",e),{enabled:!0,success:!1,message:e.message}}}static async getAllEndpoints(){try{const e=await fetch(`${this.BASE_URL}/endpoints`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`Failed to get endpoints: ${e.status}`);return await e.json()}catch(e){throw console.error("Failed to get endpoints:",e),new Error("Failed to get endpoints: "+e.message)}}static async getCoordinatorToolsByTemplate(e){console.log("[CoordinatorToolApiService] Starting to get coordinator tool, planTemplateId:",e),console.log("[CoordinatorToolApiService] Request URL:",`${this.BASE_URL}/get-get-or-new-by-template/${e}`);try{const s=await fetch(`${this.BASE_URL}/get-or-new-by-template/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(console.log("[CoordinatorToolApiService] Response status:",s.status),console.log("[CoordinatorToolApiService] Response status text:",s.statusText),s.status===404)return console.log("[CoordinatorToolApiService] 404"),null;if(!s.ok){const a=await s.text();throw console.error("[CoordinatorToolApiService] Response error content:",a),new Error(`Failed to get coordinator tools: ${s.status} - ${a}`)}const n=await s.json();return console.log("[CoordinatorToolApiService] Successfully got coordinator tool, result:",n),n}catch(s){throw console.error("[CoordinatorToolApiService] Failed to get coordinator tool:",s),new Error("Failed to get coordinator tool: "+s.message)}}static async getOrNewCoordinatorToolsByTemplate(e){console.log("[CoordinatorToolApiService] Starting to get or create coordinator tool, planTemplateId:",e),console.log("[CoordinatorToolApiService] Request URL:",`${this.BASE_URL}/get-or-new-by-template/${e}`);try{const s=await fetch(`${this.BASE_URL}/get-or-new-by-template/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(console.log("[CoordinatorToolApiService] Response status:",s.status),console.log("[CoordinatorToolApiService] Response status text:",s.statusText),!s.ok){const a=await s.text();throw console.error("[CoordinatorToolApiService] Response error content:",a),new Error(`Failed to get coordinator tools: ${s.status} - ${a}`)}const n=await s.json();return console.log("[CoordinatorToolApiService] Successfully got coordinator tool, result:",n),n}catch(s){throw console.error("[CoordinatorToolApiService] Failed to get coordinator tool:",s),new Error("Failed to get coordinator tool: "+s.message)}}static async createCoordinatorTool(e){console.log("[CoordinatorToolApiService] Starting to create coordinator tool"),console.log("[CoordinatorToolApiService] Original data:",JSON.stringify(e,null,2)),console.log("[CoordinatorToolApiService] Request URL:",`${this.BASE_URL}`);const s={id:e.id,toolName:e.toolName,toolDescription:e.toolDescription,inputSchema:e.inputSchema,planTemplateId:e.planTemplateId,httpEndpoint:e.httpEndpoint,mcpEndpoint:e.mcpEndpoint,serviceGroup:e.serviceGroup,enableInternalToolcall:e.enableInternalToolcall,enableHttpService:e.enableHttpService,enableMcpService:e.enableMcpService};console.log("[CoordinatorToolApiService] Cleaned sending data:",JSON.stringify(s,null,2));try{const n=await fetch(`${this.BASE_URL}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(console.log("[CoordinatorToolApiService] Response status:",n.status),console.log("[CoordinatorToolApiService] Response status text:",n.statusText),!n.ok){let l="Unknown error";try{const i=await n.json();l=i.message||i.error||l}catch{l=await n.text()||l}throw console.error("[CoordinatorToolApiService] Response error content:",l),new Error(`Failed to create coordinator tool: ${n.status} - ${l}`)}const a=await n.json();return console.log("[CoordinatorToolApiService] Created successfully, result:",a),a}catch(n){throw console.error("[CoordinatorToolApiService] Failed to create coordinator tool:",n),new Error("Failed to create coordinator tool: "+n.message)}}static async updateCoordinatorTool(e,s){console.log("[CoordinatorToolApiService] Starting to update coordinator tool, ID:",e),console.log("[CoordinatorToolApiService] Sending data:",s),console.log("[CoordinatorToolApiService] Request URL:",`${this.BASE_URL}/${e}`);const n={id:s.id,toolName:s.toolName,toolDescription:s.toolDescription,inputSchema:s.inputSchema,planTemplateId:s.planTemplateId,httpEndpoint:s.httpEndpoint,mcpEndpoint:s.mcpEndpoint,serviceGroup:s.serviceGroup,enableInternalToolcall:s.enableInternalToolcall,enableHttpService:s.enableHttpService,enableMcpService:s.enableMcpService};console.log("[CoordinatorToolApiService] Cleaned sending data:",n);try{const a=await fetch(`${this.BASE_URL}/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(console.log("[CoordinatorToolApiService] Response status:",a.status),console.log("[CoordinatorToolApiService] Response status text:",a.statusText),!a.ok){let i="Unknown error";try{const h=await a.json();i=h.message||h.error||i}catch{i=await a.text()||i}throw console.error("[CoordinatorToolApiService] Response error content:",i),new Error(`Failed to update coordinator tool: ${a.status} - ${i}`)}const l=await a.json();return console.log("[CoordinatorToolApiService] Updated successfully, result:",l),l}catch(a){throw console.error("[CoordinatorToolApiService] Failed to update coordinator tool:",a),new Error("Failed to update coordinator tool: "+a.message)}}static async deleteCoordinatorTool(e){console.log("[CoordinatorToolApiService] Starting to delete coordinator tool, ID:",e),console.log("[CoordinatorToolApiService] Request URL:",`${this.BASE_URL}/${e}`);try{const s=await fetch(`${this.BASE_URL}/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json"}});if(console.log("[CoordinatorToolApiService] Response status:",s.status),console.log("[CoordinatorToolApiService] Response status text:",s.statusText),!s.ok){const a=await s.text();throw console.error("[CoordinatorToolApiService] Response error content:",a),new Error(`Failed to delete coordinator tool: ${s.status} - ${a}`)}const n=await s.json();return console.log("[CoordinatorToolApiService] Deleted successfully, result:",n),n}catch(s){throw console.error("[CoordinatorToolApiService] Failed to delete coordinator tool:",s),new Error("Failed to delete coordinator tool: "+s.message)}}}ge(De,"BASE_URL","/api/coordinator-tools");class Ft{static async getParameterRequirements(e){try{const s=await fetch(`${this.BASE_URL}/${e}/parameters`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw new Error(`Failed to get parameter requirements: ${s.statusText}`);return await s.json()}catch(s){throw console.error("Error getting parameter requirements:",s),s}}}ge(Ft,"BASE_URL","/api/plan-template");const Ks={class:"modal-form wide-modal"},Xs={class:"form-section"},Ys={class:"form-item"},Qs=["placeholder"],Zs={class:"field-description"},en={class:"form-section"},tn={class:"form-item"},sn=["placeholder"],nn={class:"field-description"},on={class:"form-section"},an={class:"form-item"},ln=["placeholder"],rn={class:"field-description"},cn={class:"form-section"},dn={class:"section-title"},un={key:0,class:"params-help-text"},pn={class:"parameter-table"},hn=["onUpdate:modelValue","placeholder","readonly"],mn=["onUpdate:modelValue","placeholder"],fn={class:"form-section"},vn={class:"service-publish-options"},gn={class:"internal-toolcall-publish-option"},bn={class:"checkbox-label"},yn={class:"checkbox-text"},_n={class:"checkbox-description"},$n={class:"http-publish-option"},wn={class:"checkbox-label"},Sn={class:"checkbox-text"},kn={class:"checkbox-description"},Tn={class:"mcp-publish-option"},Pn={class:"checkbox-label"},En={class:"checkbox-text"},Cn={class:"checkbox-description"},In={key:0,class:"form-section"},Rn={class:"form-item"},An={class:"endpoint-description"},xn={class:"endpoint-container"},Dn={class:"endpoint-row"},Mn={class:"custom-select"},Fn={class:"select-input-container"},Nn={class:"input-content"},Un=["placeholder"],Ln={class:"dropdown-header"},Bn={class:"select-options"},On=["onClick"],qn={class:"option-name"},jn={class:"manual-input-section"},Vn={class:"manual-input-container"},Hn=["placeholder"],Gn={key:0,class:"form-item url-item"},zn={class:"url-container"},Wn=["title"],Jn={class:"url-text"},Kn={class:"button-container"},Xn=["disabled"],Yn=["disabled"],Qn=ue({__name:"PublishServiceModal",props:{modelValue:{type:Boolean,default:!1},planTemplateId:{default:""},planTitle:{default:""},planDescription:{default:""}},emits:["update:modelValue","published"],setup(o,{expose:e,emit:s}){const{t:n}=Pe(),a=o,l=s,i=de({get:()=>a.modelValue,set:V=>l("update:modelValue",V)}),h=N(""),g=N(""),u=N(!1),d=N(!1),b=N([]),R=N(""),M=N(!1),_=N("bottom"),y=N(""),v=N(null),$=N(!1),C=N(!1),S=N(!1),I=N(!0),m=N({parameters:[],hasParameters:!1,requirements:""}),O=N(!1),D=st({serviceName:"",userRequest:"",endpoint:"",serviceGroup:"",parameters:[]}),z=de(()=>{const V=v.value&&v.value.id;return n(V?"mcpService.updateService":"mcpService.createService")}),k=()=>{D.serviceName="",D.userRequest=a.planDescription||"",D.endpoint="",D.serviceGroup="",m.value.hasParameters||(D.parameters=[]),v.value=null,R.value="",$.value=!1},F=async()=>{try{b.value=await De.getAllEndpoints()}catch(V){console.error("Failed to load endpoints:",V),T(n("mcpService.loadEndpointsFailed")+": "+V.message,"error")}},W=async()=>{if(!a.planTemplateId){m.value={parameters:[],hasParameters:!1,requirements:""};return}O.value=!0;try{const V=await Ft.getParameterRequirements(a.planTemplateId);m.value=V,V.hasParameters&&(D.parameters=V.parameters.map(Y=>({name:Y,description:""})))}catch(V){console.error("[PublishModal] Failed to load parameter requirements:",V),V instanceof Error&&!V.message.includes("404")&&console.warn("[PublishModal] Parameter requirements not available yet, will retry later"),m.value={parameters:[],hasParameters:!1,requirements:""}}finally{O.value=!1}},Q=()=>{M.value||E(),M.value=!M.value},E=()=>{const V=document.querySelector(".custom-select");if(!V)return;const Y=V.getBoundingClientRect(),X=window.innerHeight;Y.bottom+200>X?_.value="top":_.value="bottom"},x=V=>{D.endpoint=V,M.value=!1,y.value=""},B=()=>{if(y.value.trim()){const V=y.value.trim().replace(/[^a-zA-Z0-9_/]/g,"");V&&(D.endpoint=V,b.value.includes(V)||b.value.push(V),M.value=!1,y.value="")}},J=()=>{setTimeout(()=>{y.value.trim()&&B()},200)},ee=()=>{b.value.includes(D.endpoint)&&x(D.endpoint)},Z=()=>{b.value.includes(D.endpoint)&&x(D.endpoint)},he=()=>{b.value.includes(D.endpoint)&&x(D.endpoint)},G=async()=>{if(R.value)try{await navigator.clipboard.writeText(R.value),T(n("common.copy")+" "+n("common.success"),"success")}catch(V){console.error("Copy failed:",V),T(n("common.copy")+" "+n("common.error"),"error")}},T=(V,Y)=>{Y==="success"?(g.value=V,setTimeout(()=>{g.value=""},3e3)):Y==="error"&&(h.value=V,setTimeout(()=>{h.value=""},5e3))},j=()=>{if(!D.serviceName||!D.serviceName.trim())return T(n("mcpService.toolNameRequiredError"),"error"),!1;if(!D.userRequest||!D.userRequest.trim())return T(n("mcpService.toolDescriptionRequiredError"),"error"),!1;if(!D.serviceGroup||!D.serviceGroup.trim())return T(n("mcpService.serviceGroupRequiredError"),"error"),!1;if(C.value&&(!D.endpoint||!D.endpoint.trim()))return T(n("mcpService.endpointRequiredError"),"error"),!1;if(!I.value&&!S.value&&!C.value)return T("Please select at least one service type","error"),!1;for(let V=0;V<D.parameters.length;V++){const Y=D.parameters[V];if(Y.name&&!Y.description.trim())return T(`Parameter "${Y.name}" description cannot be empty`,"error"),!1;if(Y.description&&!Y.name.trim())return T(`Parameter description "${Y.description}" corresponding name cannot be empty`,"error"),!1}return!0},U=async()=>{if(console.log("[PublishModal] Starting to handle publish request"),console.log("[PublishModal] Form data:",D),console.log("[PublishModal] Current tool:",v.value),console.log("[PublishModal] Publish as MCP service:",C.value),console.log("[PublishModal] Publish as HTTP service:",S.value),!j()){console.log("[PublishModal] Form validation failed");return}u.value=!0;try{v.value||(console.log("[PublishModal] No current tool data, getting or creating first"),v.value=await De.getOrNewCoordinatorToolsByTemplate(a.planTemplateId)),console.log("[PublishModal] Updating tool information"),v.value.toolName=D.serviceName.trim(),v.value.toolDescription=D.userRequest.trim(),v.value.serviceGroup=D.serviceGroup.trim(),v.value.planTemplateId=a.planTemplateId,v.value.enableInternalToolcall=I.value,v.value.enableHttpService=S.value,v.value.enableMcpService=C.value,C.value?v.value.mcpEndpoint=D.endpoint.trim():v.value.mcpEndpoint=void 0;const V=D.parameters.filter(X=>X.name.trim()&&X.description.trim()).map(X=>({name:X.name.trim(),description:X.description.trim(),type:"string"}));if(v.value.inputSchema=JSON.stringify(V),console.log("[PublishModal] Updated tool information:",v.value),v.value.id)console.log("[PublishModal] Updating existing tool, ID:",v.value.id),await De.updateCoordinatorTool(v.value.id,v.value);else{console.log("[PublishModal] Creating new tool");const X=await De.createCoordinatorTool(v.value);v.value=X}const Y=[];if(I.value&&Y.push("Internal Method Call"),S.value&&Y.push("HTTP Service"),C.value&&Y.push("MCP Service"),Y.length>0){console.log("[PublishModal] Step 5: Publishing service, ID:",v.value.id,"Enabled services:",Y.join(", "));const X=[];if(C.value&&v.value.mcpEndpoint){const ye=window.location.origin;X.push(`MCP: ${ye}/mcp${v.value.mcpEndpoint}`)}I.value&&X.push(`Internal Call: ${D.serviceName}`),R.value=X.join(`
`),console.log("[PublishModal] Service published successfully, service URLs:",R.value),T(n("mcpService.publishSuccess"),"success"),l("published",v.value)}else console.log("[PublishModal] Only saving tool, not publishing as any service"),T(n("mcpService.saveSuccess"),"success"),l("published",v.value)}catch(V){console.error("[PublishModal] Failed to publish service:",V),T(n("mcpService.publishFailed")+": "+V.message,"error")}finally{u.value=!1}},q=async()=>{if(!d.value&&confirm(n("mcpService.deleteConfirmMessage"))){if(!v.value||!v.value.id){T(n("mcpService.deleteFailed")+": "+n("mcpService.selectPlanTemplateFirst"),"error");return}d.value=!0;try{console.log("[PublishModal] Starting to delete MCP service, ID:",v.value.id);const V=await De.deleteCoordinatorTool(v.value.id);if(V.success)console.log("[PublishModal] Deleted successfully"),T(n("mcpService.deleteSuccess"),"success"),i.value=!1,l("published",null);else throw new Error(V.message)}catch(V){console.error("[PublishModal] Failed to delete MCP service:",V),T(n("mcpService.deleteFailed")+": "+V.message,"error")}finally{d.value=!1}}},K=async()=>{i.value&&(console.log("[PublishModal] Modal opened, starting to initialize data"),k(),await F(),await ve())},ve=async()=>{if(!a.planTemplateId){console.log("[PublishModal] "+n("mcpService.noPlanTemplateId"));return}try{console.log("[PublishModal] Starting to load coordinator tool data, planTemplateId:",a.planTemplateId);const V=await De.getOrNewCoordinatorToolsByTemplate(a.planTemplateId);console.log("[PublishModal] Get coordinator tool data result:",V),v.value=V,$.value=!!V.id;const Y=[];if(V.enableMcpService&&V.mcpEndpoint){const X=window.location.origin;Y.push(`MCP: ${X}/mcp${V.mcpEndpoint}`)}V.enableInternalToolcall&&Y.push(`Internal Call: ${V.toolName}`),R.value=Y.join(`
`),console.log("[PublishModal] Load tool data - endpointUrl:",R.value),D.serviceName=V.toolName||"",D.userRequest=V.toolDescription||a.planDescription||"",D.serviceGroup=V.serviceGroup||"",C.value=V.enableMcpService||!1,S.value=V.enableHttpService||!1,I.value=V.enableInternalToolcall||!1,V.enableMcpService&&(D.endpoint=V.mcpEndpoint||"");try{if(V.inputSchema){const X=JSON.parse(V.inputSchema);Array.isArray(X)&&X.length>0?(D.parameters=X.map(ye=>({name:ye.name||"",description:ye.description||""})),console.log("[PublishModal] Load parameters from inputSchema:",D.parameters)):console.log("[PublishModal] inputSchema is empty, keeping existing parameters:",D.parameters)}}catch(X){console.warn("[PublishModal] "+n("mcpService.parseInputSchemaFailed")+":",X),console.log("[PublishModal] Parsing failed, keeping existing parameters:",D.parameters)}console.log("[PublishModal] Form data filled:",D)}catch(V){console.error("[PublishModal] "+n("mcpService.loadToolDataFailed")+":",V),T(n("mcpService.loadToolDataFailed")+": "+V.message,"error")}};return ce(()=>a.modelValue,K),ce(()=>a.planTemplateId,(V,Y)=>{V&&V!==Y&&(Y&&V.startsWith("planTemplate-")?setTimeout(()=>{W()},1e3):W())}),we(async()=>{i.value&&(console.log("[PublishModal] Initialize when component mounted"),k(),await F(),await ve(),await W())}),e({loadParameterRequirements:W}),(V,Y)=>(p(),f(ae,null,[w(Vs,{modelValue:i.value,"onUpdate:modelValue":Y[11]||(Y[11]=X=>i.value=X),title:z.value,onConfirm:U},{footer:Me(()=>{var X;return[t("div",Kn,[$.value&&((X=v.value)!=null&&X.id)?(p(),f("button",{key:0,class:"action-btn danger",onClick:q,disabled:d.value},[d.value?(p(),le(r(P),{key:0,icon:"carbon:loading",class:"loading-icon"})):(p(),le(r(P),{key:1,icon:"carbon:trash-can"})),ne(" "+c(d.value?r(n)("mcpService.deleting"):r(n)("mcpService.delete")),1)],8,Xn)):L("",!0),t("button",{class:"action-btn primary",onClick:U,disabled:u.value},[u.value?(p(),le(r(P),{key:0,icon:"carbon:loading",class:"loading-icon"})):(p(),le(r(P),{key:1,icon:"carbon:cloud-upload"})),ne(" "+c(u.value?r(n)("mcpService.publishing"):r(n)("mcpService.publishAsService")),1)],8,Yn)])]}),default:Me(()=>[t("div",Ks,[t("div",Xs,[t("div",Ys,[t("label",null,c(r(n)("mcpService.toolNameRequired")),1),se(t("input",{type:"text","onUpdate:modelValue":Y[0]||(Y[0]=X=>D.serviceName=X),placeholder:r(n)("mcpService.toolNamePlaceholder"),class:oe({error:!D.serviceName||!D.serviceName.trim()}),required:""},null,10,Qs),[[re,D.serviceName]]),t("div",Zs,c(r(n)("mcpService.toolNameDescription")),1)])]),t("div",en,[t("div",tn,[t("label",null,c(r(n)("mcpService.toolDescriptionRequired")),1),se(t("textarea",{"onUpdate:modelValue":Y[1]||(Y[1]=X=>D.userRequest=X),placeholder:r(n)("mcpService.toolDescriptionPlaceholder"),class:oe([{error:!D.userRequest||!D.userRequest.trim()},"description-field"]),rows:"4",required:""},null,10,sn),[[re,D.userRequest]]),t("div",nn,c(r(n)("mcpService.toolDescriptionDescription")),1)])]),t("div",on,[t("div",an,[t("label",null,c(r(n)("mcpService.serviceGroup")),1),se(t("input",{type:"text","onUpdate:modelValue":Y[2]||(Y[2]=X=>D.serviceGroup=X),placeholder:r(n)("mcpService.serviceGroupPlaceholder"),class:oe({error:!D.serviceGroup||!D.serviceGroup.trim()}),required:""},null,10,ln),[[re,D.serviceGroup]]),t("div",rn,c(r(n)("mcpService.serviceGroupDescription")),1)])]),t("div",cn,[t("div",dn,c(r(n)("mcpService.parameterConfig")),1),m.value.hasParameters?(p(),f("div",un,c(r(n)("sidebar.parameterRequirementsHelp")),1)):L("",!0),t("div",pn,[t("table",null,[t("thead",null,[t("tr",null,[t("th",null,c(r(n)("mcpService.parameterName")),1),t("th",null,c(r(n)("mcpService.parameterDescription")),1)])]),t("tbody",null,[(p(!0),f(ae,null,ie(D.parameters,(X,ye)=>(p(),f("tr",{key:ye},[t("td",null,[se(t("input",{type:"text","onUpdate:modelValue":xe=>X.name=xe,placeholder:r(n)("mcpService.parameterName"),class:oe(["parameter-input",{"readonly-input":m.value.hasParameters}]),readonly:m.value.hasParameters,required:""},null,10,hn),[[re,X.name]])]),t("td",null,[se(t("input",{type:"text","onUpdate:modelValue":xe=>X.description=xe,placeholder:r(n)("mcpService.parameterDescription"),class:"parameter-input",required:""},null,8,mn),[[re,X.description]])])]))),128))])])])]),t("div",fn,[t("div",vn,[t("div",gn,[t("label",bn,[se(t("input",{type:"checkbox","onUpdate:modelValue":Y[3]||(Y[3]=X=>I.value=X),class:"checkbox-input"},null,512),[[it,I.value]]),t("span",yn,c(r(n)("mcpService.publishAsInternalToolcall")),1)]),t("div",_n,c(r(n)("mcpService.publishAsInternalToolcallDescription")),1)]),t("div",$n,[t("label",wn,[se(t("input",{type:"checkbox","onUpdate:modelValue":Y[4]||(Y[4]=X=>S.value=X),class:"checkbox-input"},null,512),[[it,S.value]]),t("span",Sn,c(r(n)("mcpService.publishAsHttpService")),1)]),t("div",kn,c(r(n)("mcpService.publishAsHttpServiceDescription")),1)]),t("div",Tn,[t("label",Pn,[se(t("input",{type:"checkbox","onUpdate:modelValue":Y[5]||(Y[5]=X=>C.value=X),class:"checkbox-input"},null,512),[[it,C.value]]),t("span",En,c(r(n)("mcpService.publishAsMcpService")),1)]),t("div",Cn,c(r(n)("mcpService.publishAsMcpServiceDescription")),1),C.value?(p(),f("div",In,[t("div",Rn,[t("label",null,c(r(n)("mcpService.endpointRequired")),1),t("div",An,c(r(n)("mcpService.endpointDescription")),1),t("div",xn,[t("div",Dn,[t("div",Mn,[t("div",Fn,[t("div",Nn,[w(r(P),{icon:"carbon:application",width:"16",class:"input-icon"}),se(t("input",{type:"text","onUpdate:modelValue":Y[6]||(Y[6]=X=>D.endpoint=X),placeholder:r(n)("mcpService.endpointPlaceholder"),class:"select-input",onFocus:Y[7]||(Y[7]=X=>M.value=!0),onInput:ee,onKeydown:jt(Z,["enter"]),onBlur:he},null,40,Un),[[re,D.endpoint]])]),t("button",{class:"select-arrow-btn",onClick:Q,title:"Expand Options"},[w(r(P),{icon:M.value?"carbon:chevron-up":"carbon:chevron-down",width:"14",class:"chevron"},null,8,["icon"])])]),M.value?(p(),f("div",{key:0,class:oe(["select-dropdown",{"dropdown-top":_.value==="top"}])},[t("div",Ln,[t("span",null,c(r(n)("mcpService.selectEndpoint")),1),t("button",{class:"close-btn",onClick:Y[8]||(Y[8]=X=>M.value=!1)},[w(r(P),{icon:"carbon:close",width:"12"})])]),t("div",Bn,[(p(!0),f(ae,null,ie(b.value,X=>(p(),f("button",{key:X,class:oe(["select-option",{active:D.endpoint===X}]),onClick:ye=>x(X)},[t("span",qn,c(X),1),D.endpoint===X?(p(),le(r(P),{key:0,icon:"carbon:checkmark",width:"14",class:"check-icon"})):L("",!0)],10,On))),128))]),t("div",jn,[t("div",Vn,[se(t("input",{type:"text","onUpdate:modelValue":Y[9]||(Y[9]=X=>y.value=X),placeholder:r(n)("mcpService.manualInput"),class:"manual-input",onKeydown:jt(B,["enter"]),onBlur:J},null,40,Hn),[[re,y.value]]),t("button",{class:"add-manual-btn",onClick:B},[w(r(P),{icon:"carbon:add",width:"14"})])])])],2)):L("",!0),M.value?(p(),f("div",{key:1,class:"backdrop",onClick:Y[10]||(Y[10]=X=>M.value=!1)})):L("",!0)])])])]),R.value?(p(),f("div",Gn,[t("label",null,c(r(n)("mcpService.mcpStreamableUrl")),1),t("div",zn,[t("div",{class:"url-display",onDblclick:G,title:r(n)("mcpService.copyUrl")+": "+R.value},[t("span",Jn,c(R.value||r(n)("mcpService.mcpStreamableUrlPlaceholder")),1),w(r(P),{icon:"carbon:copy",width:"16",class:"copy-icon"})],40,Wn)])])):L("",!0)])):L("",!0)])])])])]),_:1},8,["modelValue","title"]),h.value?(p(),f("div",{key:0,class:"error-toast",onClick:Y[12]||(Y[12]=X=>h.value="")},[w(r(P),{icon:"carbon:error"}),ne(" "+c(h.value),1)])):L("",!0),g.value?(p(),f("div",{key:1,class:"success-toast",onClick:Y[13]||(Y[13]=X=>g.value="")},[w(r(P),{icon:"carbon:checkmark"}),ne(" "+c(g.value),1)])):L("",!0)],64))}}),Zn=pe(Qn,[["__scopeId","data-v-bd88743f"]]);function ds(o,e){const s=N(!1),n=st({title:"",steps:[],directResponse:!1,planTemplateId:o.currentPlanTemplateId||"",planType:"dynamic_agent"}),a=v=>{try{if(!v){Object.assign(n,{title:"",steps:[],directResponse:!1,planTemplateId:o.currentPlanTemplateId||"",planType:"dynamic_agent"});return}const $=JSON.parse(v);n.title=$.title||"",n.directResponse=!1,n.planTemplateId=$.planTemplateId||o.currentPlanTemplateId||"",n.planType=$.planType||"dynamic_agent",n.steps=($.steps||[]).map(C=>({stepRequirement:C.stepRequirement||"",agentName:C.agentName||"",modelName:C.modelName||null,selectedToolKeys:C.selectedToolKeys||[],terminateColumns:C.terminateColumns||""}))}catch($){console.warn("Failed to parse JSON content:",$)}},l=()=>{try{const v={title:n.title,steps:n.steps.map($=>({stepRequirement:$.stepRequirement,agentName:$.agentName,modelName:$.modelName||"",selectedToolKeys:$.selectedToolKeys,terminateColumns:$.terminateColumns})),directResponse:n.directResponse,planTemplateId:n.planTemplateId,planType:n.planType};return JSON.stringify(v,null,2)}catch(v){return console.error("Failed to convert visual data to JSON:",v),"{}"}},i=de(()=>l());return ce(()=>o.jsonContent,v=>{a(v)},{immediate:!0}),ce(n,()=>{const v=l();e("update:jsonContent",v)},{deep:!0}),ce(()=>o.currentPlanTemplateId,v=>{v&&(n.planTemplateId=v)}),{showJsonPreview:s,parsedData:n,formattedJsonOutput:i,addStep:()=>{const v={stepRequirement:"",agentName:"ConfigurableDynaAgent",modelName:null,selectedToolKeys:[],terminateColumns:"",agentType:"",stepContent:""};n.steps.push(v)},removeStep:v=>{v>=0&&v<n.steps.length&&n.steps.splice(v,1)},moveStepUp:v=>{if(v>0){const $=n.steps.splice(v,1)[0];n.steps.splice(v-1,0,$)}},moveStepDown:v=>{if(v<n.steps.length-1){const $=n.steps.splice(v,1)[0];n.steps.splice(v+1,0,$)}},toggleJsonPreview:()=>{s.value=!s.value},closeJsonPreview:()=>{s.value=!1},handleRollback:()=>{e("rollback")},handleRestore:()=>{e("restore")},handleSave:()=>{e("save")}}}const eo={class:"config-section"},to={class:"section-header"},so={class:"visual-editor"},no={class:"plan-basic-info"},oo={class:"form-row"},ao={class:"form-label"},lo=["placeholder"],ro={class:"steps-section"},io={class:"steps-header"},co={class:"form-label"},uo={class:"steps-container"},po={class:"step-header"},ho={class:"step-number"},mo={class:"step-actions"},fo=["onClick","disabled","title"],vo=["onClick","disabled","title"],go=["onClick","title"],bo={class:"step-content"},yo={class:"form-row"},_o={class:"form-label"},$o={class:"agent-selector"},wo=["onUpdate:modelValue"],So={value:""},ko=["title"],To={class:"form-row"},Po={class:"form-label"},Eo=["onUpdate:modelValue","placeholder"],Co={class:"form-row"},Io={class:"form-label"},Ro=["onUpdate:modelValue","placeholder"],Ao={key:0,class:"empty-steps"},xo={class:"plan-id-section"},Do={class:"form-row"},Mo={class:"form-label"},Fo={key:0,class:"json-preview"},No={class:"preview-header"},Uo={class:"form-label"},Lo={class:"json-code"},Bo={class:"editor-footer"},Oo={class:"section-actions"},qo=["disabled","title"],jo=["disabled","title"],Vo=["disabled"],Ho=ue({__name:"JsonEditor",props:{jsonContent:{},canRollback:{type:Boolean},canRestore:{type:Boolean},isGenerating:{type:Boolean},isExecuting:{type:Boolean},hiddenFields:{default:()=>["currentPlanId","userRequest","rootPlanId"]},currentPlanTemplateId:{}},emits:["rollback","restore","save","update:jsonContent"],setup(o,{emit:e}){const s=o,n=e,{showJsonPreview:a,parsedData:l,formattedJsonOutput:i,addStep:h,removeStep:g,moveStepUp:u,moveStepDown:d,handleRollback:b,handleRestore:R,handleSave:M,toggleJsonPreview:_,closeJsonPreview:y}=ds(s,n),v=$=>{const C=$.target;if(!C)return;C.style.height="auto";const S=20,I=Math.ceil(C.scrollHeight/S),m=4,O=12,D=Math.max(m,Math.min(O,I)),z=D*S;C.style.height=`${z}px`,C.rows=D,I>O?C.style.overflowY="auto":C.style.overflowY="hidden"};return($,C)=>(p(),f("div",eo,[t("div",to,[w(r(P),{icon:"carbon:code",width:"16"}),t("span",null,c($.$t("sidebar.jsonTemplate")),1)]),t("div",so,[t("div",no,[t("div",oo,[t("label",ao,c($.$t("sidebar.title")),1),se(t("input",{"onUpdate:modelValue":C[0]||(C[0]=S=>r(l).title=S),type:"text",class:"form-input",placeholder:$.$t("sidebar.titlePlaceholder")},null,8,lo),[[re,r(l).title]])])]),t("div",ro,[t("div",io,[t("label",co,c($.$t("sidebar.tasks")),1)]),t("div",uo,[(p(!0),f(ae,null,ie(r(l).steps,(S,I)=>(p(),f("div",{key:I,class:"step-item"},[t("div",po,[t("span",ho,c($.$t("sidebar.subtask"))+" "+c(I+1),1),t("div",mo,[t("button",{onClick:m=>r(u)(I),disabled:I===0,class:"btn btn-xs",title:$.$t("sidebar.moveUp")},[w(r(P),{icon:"carbon:chevron-up",width:"12"})],8,fo),t("button",{onClick:m=>r(d)(I),disabled:I===r(l).steps.length-1,class:"btn btn-xs",title:$.$t("sidebar.moveDown")},[w(r(P),{icon:"carbon:chevron-down",width:"12"})],8,vo),t("button",{onClick:m=>r(g)(I),class:"btn btn-xs btn-danger",title:$.$t("sidebar.removeStep")},[w(r(P),{icon:"carbon:trash-can",width:"12"})],8,go)])]),t("div",bo,[t("div",yo,[t("label",_o,c($.$t("sidebar.agent")),1),t("div",$o,[se(t("select",{"onUpdate:modelValue":m=>S.agentName=m,class:"form-select agent-select"},[t("option",So,c($.$t("sidebar.selectAgent")),1),C[10]||(C[10]=ns('<option value="DEFAULT_AGENT" data-v-f7391c35>DEFAULT_AGENT</option><option value="BROWSER_AGENT" data-v-f7391c35>BROWSER_AGENT</option><option value="TEXT_FILE_AGENT" data-v-f7391c35>TEXT_FILE_AGENT</option><option value="JSX_GENERATOR_AGENT" data-v-f7391c35>JSX_GENERATOR_AGENT</option><option value="FILE_MANAGER_AGENT" data-v-f7391c35>FILE_MANAGER_AGENT</option>',5))],8,wo),[[nt,S.agentName]]),t("button",{onClick:C[1]||(C[1]=(...m)=>r(h)&&r(h)(...m)),class:"btn btn-sm btn-add-step",title:$.$t("sidebar.addStep")},[w(r(P),{icon:"carbon:add",width:"14"})],8,ko)])]),t("div",To,[t("label",Po,c($.$t("sidebar.stepRequirement")),1),se(t("textarea",{"onUpdate:modelValue":m=>S.stepContent=m,class:"form-textarea auto-resize",placeholder:$.$t("sidebar.stepRequirementPlaceholder"),rows:"4",onInput:C[2]||(C[2]=m=>v(m))},null,40,Eo),[[re,S.stepContent]])]),t("div",Co,[t("label",Io,c($.$t("sidebar.terminateColumns")),1),se(t("input",{"onUpdate:modelValue":m=>S.terminateColumns=m,type:"text",class:"form-input",placeholder:$.$t("sidebar.terminateColumnsPlaceholder")},null,8,Ro),[[re,S.terminateColumns]])])])]))),128)),r(l).steps.length===0?(p(),f("div",Ao,[w(r(P),{icon:"carbon:add-alt",width:"32",class:"empty-icon"}),t("p",null,c($.$t("sidebar.noSteps")),1),t("button",{onClick:C[3]||(C[3]=(...S)=>r(h)&&r(h)(...S)),class:"btn btn-primary"},[w(r(P),{icon:"carbon:add",width:"14"}),ne(" "+c($.$t("sidebar.addFirstStep")),1)])])):L("",!0)])]),t("div",xo,[t("div",Do,[t("label",Mo,c($.$t("sidebar.planId")),1),se(t("input",{"onUpdate:modelValue":C[4]||(C[4]=S=>r(l).planTemplateId=S),type:"text",class:"form-input",placeholder:"planTemplate-1756109892045"},null,512),[[re,r(l).planTemplateId]])])]),r(a)?(p(),f("div",Fo,[t("div",No,[t("label",Uo,c($.$t("sidebar.jsonPreview")),1),t("button",{onClick:C[5]||(C[5]=(...S)=>r(y)&&r(y)(...S)),class:"btn btn-xs"},[w(r(P),{icon:"carbon:close",width:"12"})])]),t("pre",Lo,c(r(i)),1)])):L("",!0),t("div",Bo,[t("button",{onClick:C[6]||(C[6]=(...S)=>r(_)&&r(_)(...S)),class:"btn btn-sm btn-secondary"},[w(r(P),{icon:"carbon:code",width:"14"}),ne(" "+c(r(a)?$.$t("sidebar.hideJson"):$.$t("sidebar.showJson")),1)]),t("div",Oo,[t("button",{class:"btn btn-sm",onClick:C[7]||(C[7]=(...S)=>r(b)&&r(b)(...S)),disabled:!($.canRollback??!1),title:$.$t("sidebar.rollback")},[w(r(P),{icon:"carbon:undo",width:"14"})],8,qo),t("button",{class:"btn btn-sm",onClick:C[8]||(C[8]=(...S)=>r(R)&&r(R)(...S)),disabled:!($.canRestore??!1),title:$.$t("sidebar.restore")},[w(r(P),{icon:"carbon:redo",width:"14"})],8,jo),t("button",{class:"btn btn-primary",onClick:C[9]||(C[9]=(...S)=>r(M)&&r(M)(...S)),disabled:$.isGenerating||$.isExecuting},[w(r(P),{icon:"carbon:save",width:"14"}),C[11]||(C[11]=ne(" Save "))],8,Vo)])])])]))}}),Go=pe(Ho,[["__scopeId","data-v-f7391c35"]]);function us(o,e){return function(){return o.apply(e,arguments)}}const{toString:zo}=Object.prototype,{getPrototypeOf:Nt}=Object,{iterator:ft,toStringTag:ps}=Symbol,vt=(o=>e=>{const s=zo.call(e);return o[s]||(o[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Ie=o=>(o=o.toLowerCase(),e=>vt(e)===o),gt=o=>e=>typeof e===o,{isArray:We}=Array,tt=gt("undefined");function Wo(o){return o!==null&&!tt(o)&&o.constructor!==null&&!tt(o.constructor)&&_e(o.constructor.isBuffer)&&o.constructor.isBuffer(o)}const hs=Ie("ArrayBuffer");function Jo(o){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(o):e=o&&o.buffer&&hs(o.buffer),e}const Ko=gt("string"),_e=gt("function"),ms=gt("number"),bt=o=>o!==null&&typeof o=="object",Xo=o=>o===!0||o===!1,ct=o=>{if(vt(o)!=="object")return!1;const e=Nt(o);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(ps in o)&&!(ft in o)},Yo=Ie("Date"),Qo=Ie("File"),Zo=Ie("Blob"),ea=Ie("FileList"),ta=o=>bt(o)&&_e(o.pipe),sa=o=>{let e;return o&&(typeof FormData=="function"&&o instanceof FormData||_e(o.append)&&((e=vt(o))==="formdata"||e==="object"&&_e(o.toString)&&o.toString()==="[object FormData]"))},na=Ie("URLSearchParams"),[oa,aa,la,ra]=["ReadableStream","Request","Response","Headers"].map(Ie),ia=o=>o.trim?o.trim():o.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ot(o,e,{allOwnKeys:s=!1}={}){if(o===null||typeof o>"u")return;let n,a;if(typeof o!="object"&&(o=[o]),We(o))for(n=0,a=o.length;n<a;n++)e.call(null,o[n],n,o);else{const l=s?Object.getOwnPropertyNames(o):Object.keys(o),i=l.length;let h;for(n=0;n<i;n++)h=l[n],e.call(null,o[h],h,o)}}function fs(o,e){e=e.toLowerCase();const s=Object.keys(o);let n=s.length,a;for(;n-- >0;)if(a=s[n],e===a.toLowerCase())return a;return null}const Oe=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,vs=o=>!tt(o)&&o!==Oe;function Et(){const{caseless:o}=vs(this)&&this||{},e={},s=(n,a)=>{const l=o&&fs(e,a)||a;ct(e[l])&&ct(n)?e[l]=Et(e[l],n):ct(n)?e[l]=Et({},n):We(n)?e[l]=n.slice():e[l]=n};for(let n=0,a=arguments.length;n<a;n++)arguments[n]&&ot(arguments[n],s);return e}const ca=(o,e,s,{allOwnKeys:n}={})=>(ot(e,(a,l)=>{s&&_e(a)?o[l]=us(a,s):o[l]=a},{allOwnKeys:n}),o),da=o=>(o.charCodeAt(0)===65279&&(o=o.slice(1)),o),ua=(o,e,s,n)=>{o.prototype=Object.create(e.prototype,n),o.prototype.constructor=o,Object.defineProperty(o,"super",{value:e.prototype}),s&&Object.assign(o.prototype,s)},pa=(o,e,s,n)=>{let a,l,i;const h={};if(e=e||{},o==null)return e;do{for(a=Object.getOwnPropertyNames(o),l=a.length;l-- >0;)i=a[l],(!n||n(i,o,e))&&!h[i]&&(e[i]=o[i],h[i]=!0);o=s!==!1&&Nt(o)}while(o&&(!s||s(o,e))&&o!==Object.prototype);return e},ha=(o,e,s)=>{o=String(o),(s===void 0||s>o.length)&&(s=o.length),s-=e.length;const n=o.indexOf(e,s);return n!==-1&&n===s},ma=o=>{if(!o)return null;if(We(o))return o;let e=o.length;if(!ms(e))return null;const s=new Array(e);for(;e-- >0;)s[e]=o[e];return s},fa=(o=>e=>o&&e instanceof o)(typeof Uint8Array<"u"&&Nt(Uint8Array)),va=(o,e)=>{const n=(o&&o[ft]).call(o);let a;for(;(a=n.next())&&!a.done;){const l=a.value;e.call(o,l[0],l[1])}},ga=(o,e)=>{let s;const n=[];for(;(s=o.exec(e))!==null;)n.push(s);return n},ba=Ie("HTMLFormElement"),ya=o=>o.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,a){return n.toUpperCase()+a}),Vt=(({hasOwnProperty:o})=>(e,s)=>o.call(e,s))(Object.prototype),_a=Ie("RegExp"),gs=(o,e)=>{const s=Object.getOwnPropertyDescriptors(o),n={};ot(s,(a,l)=>{let i;(i=e(a,l,o))!==!1&&(n[l]=i||a)}),Object.defineProperties(o,n)},$a=o=>{gs(o,(e,s)=>{if(_e(o)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=o[s];if(_e(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},wa=(o,e)=>{const s={},n=a=>{a.forEach(l=>{s[l]=!0})};return We(o)?n(o):n(String(o).split(e)),s},Sa=()=>{},ka=(o,e)=>o!=null&&Number.isFinite(o=+o)?o:e;function Ta(o){return!!(o&&_e(o.append)&&o[ps]==="FormData"&&o[ft])}const Pa=o=>{const e=new Array(10),s=(n,a)=>{if(bt(n)){if(e.indexOf(n)>=0)return;if(!("toJSON"in n)){e[a]=n;const l=We(n)?[]:{};return ot(n,(i,h)=>{const g=s(i,a+1);!tt(g)&&(l[h]=g)}),e[a]=void 0,l}}return n};return s(o,0)},Ea=Ie("AsyncFunction"),Ca=o=>o&&(bt(o)||_e(o))&&_e(o.then)&&_e(o.catch),bs=((o,e)=>o?setImmediate:e?((s,n)=>(Oe.addEventListener("message",({source:a,data:l})=>{a===Oe&&l===s&&n.length&&n.shift()()},!1),a=>{n.push(a),Oe.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",_e(Oe.postMessage)),Ia=typeof queueMicrotask<"u"?queueMicrotask.bind(Oe):typeof process<"u"&&process.nextTick||bs,Ra=o=>o!=null&&_e(o[ft]),A={isArray:We,isArrayBuffer:hs,isBuffer:Wo,isFormData:sa,isArrayBufferView:Jo,isString:Ko,isNumber:ms,isBoolean:Xo,isObject:bt,isPlainObject:ct,isReadableStream:oa,isRequest:aa,isResponse:la,isHeaders:ra,isUndefined:tt,isDate:Yo,isFile:Qo,isBlob:Zo,isRegExp:_a,isFunction:_e,isStream:ta,isURLSearchParams:na,isTypedArray:fa,isFileList:ea,forEach:ot,merge:Et,extend:ca,trim:ia,stripBOM:da,inherits:ua,toFlatObject:pa,kindOf:vt,kindOfTest:Ie,endsWith:ha,toArray:ma,forEachEntry:va,matchAll:ga,isHTMLForm:ba,hasOwnProperty:Vt,hasOwnProp:Vt,reduceDescriptors:gs,freezeMethods:$a,toObjectSet:wa,toCamelCase:ya,noop:Sa,toFiniteNumber:ka,findKey:fs,global:Oe,isContextDefined:vs,isSpecCompliantForm:Ta,toJSONObject:Pa,isAsyncFn:Ea,isThenable:Ca,setImmediate:bs,asap:Ia,isIterable:Ra};function te(o,e,s,n,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=o,this.name="AxiosError",e&&(this.code=e),s&&(this.config=s),n&&(this.request=n),a&&(this.response=a,this.status=a.status?a.status:null)}A.inherits(te,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:A.toJSONObject(this.config),code:this.code,status:this.status}}});const ys=te.prototype,_s={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(o=>{_s[o]={value:o}});Object.defineProperties(te,_s);Object.defineProperty(ys,"isAxiosError",{value:!0});te.from=(o,e,s,n,a,l)=>{const i=Object.create(ys);return A.toFlatObject(o,i,function(g){return g!==Error.prototype},h=>h!=="isAxiosError"),te.call(i,o.message,e,s,n,a),i.cause=o,i.name=o.name,l&&Object.assign(i,l),i};const Aa=null;function Ct(o){return A.isPlainObject(o)||A.isArray(o)}function $s(o){return A.endsWith(o,"[]")?o.slice(0,-2):o}function Ht(o,e,s){return o?o.concat(e).map(function(a,l){return a=$s(a),!s&&l?"["+a+"]":a}).join(s?".":""):e}function xa(o){return A.isArray(o)&&!o.some(Ct)}const Da=A.toFlatObject(A,{},null,function(e){return/^is[A-Z]/.test(e)});function yt(o,e,s){if(!A.isObject(o))throw new TypeError("target must be an object");e=e||new FormData,s=A.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,v){return!A.isUndefined(v[y])});const n=s.metaTokens,a=s.visitor||d,l=s.dots,i=s.indexes,g=(s.Blob||typeof Blob<"u"&&Blob)&&A.isSpecCompliantForm(e);if(!A.isFunction(a))throw new TypeError("visitor must be a function");function u(_){if(_===null)return"";if(A.isDate(_))return _.toISOString();if(!g&&A.isBlob(_))throw new te("Blob is not supported. Use a Buffer instead.");return A.isArrayBuffer(_)||A.isTypedArray(_)?g&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function d(_,y,v){let $=_;if(_&&!v&&typeof _=="object"){if(A.endsWith(y,"{}"))y=n?y:y.slice(0,-2),_=JSON.stringify(_);else if(A.isArray(_)&&xa(_)||(A.isFileList(_)||A.endsWith(y,"[]"))&&($=A.toArray(_)))return y=$s(y),$.forEach(function(S,I){!(A.isUndefined(S)||S===null)&&e.append(i===!0?Ht([y],I,l):i===null?y:y+"[]",u(S))}),!1}return Ct(_)?!0:(e.append(Ht(v,y,l),u(_)),!1)}const b=[],R=Object.assign(Da,{defaultVisitor:d,convertValue:u,isVisitable:Ct});function M(_,y){if(!A.isUndefined(_)){if(b.indexOf(_)!==-1)throw Error("Circular reference detected in "+y.join("."));b.push(_),A.forEach(_,function($,C){(!(A.isUndefined($)||$===null)&&a.call(e,$,A.isString(C)?C.trim():C,y,R))===!0&&M($,y?y.concat(C):[C])}),b.pop()}}if(!A.isObject(o))throw new TypeError("data must be an object");return M(o),e}function Gt(o){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(o).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function Ut(o,e){this._pairs=[],o&&yt(o,this,e)}const ws=Ut.prototype;ws.append=function(e,s){this._pairs.push([e,s])};ws.toString=function(e){const s=e?function(n){return e.call(this,n,Gt)}:Gt;return this._pairs.map(function(a){return s(a[0])+"="+s(a[1])},"").join("&")};function Ma(o){return encodeURIComponent(o).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ss(o,e,s){if(!e)return o;const n=s&&s.encode||Ma;A.isFunction(s)&&(s={serialize:s});const a=s&&s.serialize;let l;if(a?l=a(e,s):l=A.isURLSearchParams(e)?e.toString():new Ut(e,s).toString(n),l){const i=o.indexOf("#");i!==-1&&(o=o.slice(0,i)),o+=(o.indexOf("?")===-1?"?":"&")+l}return o}class zt{constructor(){this.handlers=[]}use(e,s,n){return this.handlers.push({fulfilled:e,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){A.forEach(this.handlers,function(n){n!==null&&e(n)})}}const ks={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fa=typeof URLSearchParams<"u"?URLSearchParams:Ut,Na=typeof FormData<"u"?FormData:null,Ua=typeof Blob<"u"?Blob:null,La={isBrowser:!0,classes:{URLSearchParams:Fa,FormData:Na,Blob:Ua},protocols:["http","https","file","blob","url","data"]},Lt=typeof window<"u"&&typeof document<"u",It=typeof navigator=="object"&&navigator||void 0,Ba=Lt&&(!It||["ReactNative","NativeScript","NS"].indexOf(It.product)<0),Oa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",qa=Lt&&window.location.href||"http://localhost",ja=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Lt,hasStandardBrowserEnv:Ba,hasStandardBrowserWebWorkerEnv:Oa,navigator:It,origin:qa},Symbol.toStringTag,{value:"Module"})),be={...ja,...La};function Va(o,e){return yt(o,new be.classes.URLSearchParams,Object.assign({visitor:function(s,n,a,l){return be.isNode&&A.isBuffer(s)?(this.append(n,s.toString("base64")),!1):l.defaultVisitor.apply(this,arguments)}},e))}function Ha(o){return A.matchAll(/\w+|\[(\w*)]/g,o).map(e=>e[0]==="[]"?"":e[1]||e[0])}function Ga(o){const e={},s=Object.keys(o);let n;const a=s.length;let l;for(n=0;n<a;n++)l=s[n],e[l]=o[l];return e}function Ts(o){function e(s,n,a,l){let i=s[l++];if(i==="__proto__")return!0;const h=Number.isFinite(+i),g=l>=s.length;return i=!i&&A.isArray(a)?a.length:i,g?(A.hasOwnProp(a,i)?a[i]=[a[i],n]:a[i]=n,!h):((!a[i]||!A.isObject(a[i]))&&(a[i]=[]),e(s,n,a[i],l)&&A.isArray(a[i])&&(a[i]=Ga(a[i])),!h)}if(A.isFormData(o)&&A.isFunction(o.entries)){const s={};return A.forEachEntry(o,(n,a)=>{e(Ha(n),a,s,0)}),s}return null}function za(o,e,s){if(A.isString(o))try{return(e||JSON.parse)(o),A.trim(o)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(o)}const at={transitional:ks,adapter:["xhr","http","fetch"],transformRequest:[function(e,s){const n=s.getContentType()||"",a=n.indexOf("application/json")>-1,l=A.isObject(e);if(l&&A.isHTMLForm(e)&&(e=new FormData(e)),A.isFormData(e))return a?JSON.stringify(Ts(e)):e;if(A.isArrayBuffer(e)||A.isBuffer(e)||A.isStream(e)||A.isFile(e)||A.isBlob(e)||A.isReadableStream(e))return e;if(A.isArrayBufferView(e))return e.buffer;if(A.isURLSearchParams(e))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let h;if(l){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Va(e,this.formSerializer).toString();if((h=A.isFileList(e))||n.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return yt(h?{"files[]":e}:e,g&&new g,this.formSerializer)}}return l||a?(s.setContentType("application/json",!1),za(e)):e}],transformResponse:[function(e){const s=this.transitional||at.transitional,n=s&&s.forcedJSONParsing,a=this.responseType==="json";if(A.isResponse(e)||A.isReadableStream(e))return e;if(e&&A.isString(e)&&(n&&!this.responseType||a)){const i=!(s&&s.silentJSONParsing)&&a;try{return JSON.parse(e)}catch(h){if(i)throw h.name==="SyntaxError"?te.from(h,te.ERR_BAD_RESPONSE,this,null,this.response):h}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:be.classes.FormData,Blob:be.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};A.forEach(["delete","get","head","post","put","patch"],o=>{at.headers[o]={}});const Wa=A.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ja=o=>{const e={};let s,n,a;return o&&o.split(`
`).forEach(function(i){a=i.indexOf(":"),s=i.substring(0,a).trim().toLowerCase(),n=i.substring(a+1).trim(),!(!s||e[s]&&Wa[s])&&(s==="set-cookie"?e[s]?e[s].push(n):e[s]=[n]:e[s]=e[s]?e[s]+", "+n:n)}),e},Wt=Symbol("internals");function Ye(o){return o&&String(o).trim().toLowerCase()}function dt(o){return o===!1||o==null?o:A.isArray(o)?o.map(dt):String(o)}function Ka(o){const e=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(o);)e[n[1]]=n[2];return e}const Xa=o=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(o.trim());function St(o,e,s,n,a){if(A.isFunction(n))return n.call(this,e,s);if(a&&(e=s),!!A.isString(e)){if(A.isString(n))return e.indexOf(n)!==-1;if(A.isRegExp(n))return n.test(e)}}function Ya(o){return o.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,s,n)=>s.toUpperCase()+n)}function Qa(o,e){const s=A.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(o,n+s,{value:function(a,l,i){return this[n].call(this,e,a,l,i)},configurable:!0})})}let $e=class{constructor(e){e&&this.set(e)}set(e,s,n){const a=this;function l(h,g,u){const d=Ye(g);if(!d)throw new Error("header name must be a non-empty string");const b=A.findKey(a,d);(!b||a[b]===void 0||u===!0||u===void 0&&a[b]!==!1)&&(a[b||g]=dt(h))}const i=(h,g)=>A.forEach(h,(u,d)=>l(u,d,g));if(A.isPlainObject(e)||e instanceof this.constructor)i(e,s);else if(A.isString(e)&&(e=e.trim())&&!Xa(e))i(Ja(e),s);else if(A.isObject(e)&&A.isIterable(e)){let h={},g,u;for(const d of e){if(!A.isArray(d))throw TypeError("Object iterator must return a key-value pair");h[u=d[0]]=(g=h[u])?A.isArray(g)?[...g,d[1]]:[g,d[1]]:d[1]}i(h,s)}else e!=null&&l(s,e,n);return this}get(e,s){if(e=Ye(e),e){const n=A.findKey(this,e);if(n){const a=this[n];if(!s)return a;if(s===!0)return Ka(a);if(A.isFunction(s))return s.call(this,a,n);if(A.isRegExp(s))return s.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,s){if(e=Ye(e),e){const n=A.findKey(this,e);return!!(n&&this[n]!==void 0&&(!s||St(this,this[n],n,s)))}return!1}delete(e,s){const n=this;let a=!1;function l(i){if(i=Ye(i),i){const h=A.findKey(n,i);h&&(!s||St(n,n[h],h,s))&&(delete n[h],a=!0)}}return A.isArray(e)?e.forEach(l):l(e),a}clear(e){const s=Object.keys(this);let n=s.length,a=!1;for(;n--;){const l=s[n];(!e||St(this,this[l],l,e,!0))&&(delete this[l],a=!0)}return a}normalize(e){const s=this,n={};return A.forEach(this,(a,l)=>{const i=A.findKey(n,l);if(i){s[i]=dt(a),delete s[l];return}const h=e?Ya(l):String(l).trim();h!==l&&delete s[l],s[h]=dt(a),n[h]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const s=Object.create(null);return A.forEach(this,(n,a)=>{n!=null&&n!==!1&&(s[a]=e&&A.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,s])=>e+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...s){const n=new this(e);return s.forEach(a=>n.set(a)),n}static accessor(e){const n=(this[Wt]=this[Wt]={accessors:{}}).accessors,a=this.prototype;function l(i){const h=Ye(i);n[h]||(Qa(a,i),n[h]=!0)}return A.isArray(e)?e.forEach(l):l(e),this}};$e.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);A.reduceDescriptors($e.prototype,({value:o},e)=>{let s=e[0].toUpperCase()+e.slice(1);return{get:()=>o,set(n){this[s]=n}}});A.freezeMethods($e);function kt(o,e){const s=this||at,n=e||s,a=$e.from(n.headers);let l=n.data;return A.forEach(o,function(h){l=h.call(s,l,a.normalize(),e?e.status:void 0)}),a.normalize(),l}function Ps(o){return!!(o&&o.__CANCEL__)}function Je(o,e,s){te.call(this,o??"canceled",te.ERR_CANCELED,e,s),this.name="CanceledError"}A.inherits(Je,te,{__CANCEL__:!0});function Es(o,e,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?o(s):e(new te("Request failed with status code "+s.status,[te.ERR_BAD_REQUEST,te.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Za(o){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(o);return e&&e[1]||""}function el(o,e){o=o||10;const s=new Array(o),n=new Array(o);let a=0,l=0,i;return e=e!==void 0?e:1e3,function(g){const u=Date.now(),d=n[l];i||(i=u),s[a]=g,n[a]=u;let b=l,R=0;for(;b!==a;)R+=s[b++],b=b%o;if(a=(a+1)%o,a===l&&(l=(l+1)%o),u-i<e)return;const M=d&&u-d;return M?Math.round(R*1e3/M):void 0}}function tl(o,e){let s=0,n=1e3/e,a,l;const i=(u,d=Date.now())=>{s=d,a=null,l&&(clearTimeout(l),l=null),o.apply(null,u)};return[(...u)=>{const d=Date.now(),b=d-s;b>=n?i(u,d):(a=u,l||(l=setTimeout(()=>{l=null,i(a)},n-b)))},()=>a&&i(a)]}const ht=(o,e,s=3)=>{let n=0;const a=el(50,250);return tl(l=>{const i=l.loaded,h=l.lengthComputable?l.total:void 0,g=i-n,u=a(g),d=i<=h;n=i;const b={loaded:i,total:h,progress:h?i/h:void 0,bytes:g,rate:u||void 0,estimated:u&&h&&d?(h-i)/u:void 0,event:l,lengthComputable:h!=null,[e?"download":"upload"]:!0};o(b)},s)},Jt=(o,e)=>{const s=o!=null;return[n=>e[0]({lengthComputable:s,total:o,loaded:n}),e[1]]},Kt=o=>(...e)=>A.asap(()=>o(...e)),sl=be.hasStandardBrowserEnv?((o,e)=>s=>(s=new URL(s,be.origin),o.protocol===s.protocol&&o.host===s.host&&(e||o.port===s.port)))(new URL(be.origin),be.navigator&&/(msie|trident)/i.test(be.navigator.userAgent)):()=>!0,nl=be.hasStandardBrowserEnv?{write(o,e,s,n,a,l){const i=[o+"="+encodeURIComponent(e)];A.isNumber(s)&&i.push("expires="+new Date(s).toGMTString()),A.isString(n)&&i.push("path="+n),A.isString(a)&&i.push("domain="+a),l===!0&&i.push("secure"),document.cookie=i.join("; ")},read(o){const e=document.cookie.match(new RegExp("(^|;\\s*)("+o+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(o){this.write(o,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ol(o){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(o)}function al(o,e){return e?o.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):o}function Cs(o,e,s){let n=!ol(e);return o&&(n||s==!1)?al(o,e):e}const Xt=o=>o instanceof $e?{...o}:o;function Ve(o,e){e=e||{};const s={};function n(u,d,b,R){return A.isPlainObject(u)&&A.isPlainObject(d)?A.merge.call({caseless:R},u,d):A.isPlainObject(d)?A.merge({},d):A.isArray(d)?d.slice():d}function a(u,d,b,R){if(A.isUndefined(d)){if(!A.isUndefined(u))return n(void 0,u,b,R)}else return n(u,d,b,R)}function l(u,d){if(!A.isUndefined(d))return n(void 0,d)}function i(u,d){if(A.isUndefined(d)){if(!A.isUndefined(u))return n(void 0,u)}else return n(void 0,d)}function h(u,d,b){if(b in e)return n(u,d);if(b in o)return n(void 0,u)}const g={url:l,method:l,data:l,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:h,headers:(u,d,b)=>a(Xt(u),Xt(d),b,!0)};return A.forEach(Object.keys(Object.assign({},o,e)),function(d){const b=g[d]||a,R=b(o[d],e[d],d);A.isUndefined(R)&&b!==h||(s[d]=R)}),s}const Is=o=>{const e=Ve({},o);let{data:s,withXSRFToken:n,xsrfHeaderName:a,xsrfCookieName:l,headers:i,auth:h}=e;e.headers=i=$e.from(i),e.url=Ss(Cs(e.baseURL,e.url,e.allowAbsoluteUrls),o.params,o.paramsSerializer),h&&i.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let g;if(A.isFormData(s)){if(be.hasStandardBrowserEnv||be.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((g=i.getContentType())!==!1){const[u,...d]=g?g.split(";").map(b=>b.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...d].join("; "))}}if(be.hasStandardBrowserEnv&&(n&&A.isFunction(n)&&(n=n(e)),n||n!==!1&&sl(e.url))){const u=a&&l&&nl.read(l);u&&i.set(a,u)}return e},ll=typeof XMLHttpRequest<"u",rl=ll&&function(o){return new Promise(function(s,n){const a=Is(o);let l=a.data;const i=$e.from(a.headers).normalize();let{responseType:h,onUploadProgress:g,onDownloadProgress:u}=a,d,b,R,M,_;function y(){M&&M(),_&&_(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let v=new XMLHttpRequest;v.open(a.method.toUpperCase(),a.url,!0),v.timeout=a.timeout;function $(){if(!v)return;const S=$e.from("getAllResponseHeaders"in v&&v.getAllResponseHeaders()),m={data:!h||h==="text"||h==="json"?v.responseText:v.response,status:v.status,statusText:v.statusText,headers:S,config:o,request:v};Es(function(D){s(D),y()},function(D){n(D),y()},m),v=null}"onloadend"in v?v.onloadend=$:v.onreadystatechange=function(){!v||v.readyState!==4||v.status===0&&!(v.responseURL&&v.responseURL.indexOf("file:")===0)||setTimeout($)},v.onabort=function(){v&&(n(new te("Request aborted",te.ECONNABORTED,o,v)),v=null)},v.onerror=function(){n(new te("Network Error",te.ERR_NETWORK,o,v)),v=null},v.ontimeout=function(){let I=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const m=a.transitional||ks;a.timeoutErrorMessage&&(I=a.timeoutErrorMessage),n(new te(I,m.clarifyTimeoutError?te.ETIMEDOUT:te.ECONNABORTED,o,v)),v=null},l===void 0&&i.setContentType(null),"setRequestHeader"in v&&A.forEach(i.toJSON(),function(I,m){v.setRequestHeader(m,I)}),A.isUndefined(a.withCredentials)||(v.withCredentials=!!a.withCredentials),h&&h!=="json"&&(v.responseType=a.responseType),u&&([R,_]=ht(u,!0),v.addEventListener("progress",R)),g&&v.upload&&([b,M]=ht(g),v.upload.addEventListener("progress",b),v.upload.addEventListener("loadend",M)),(a.cancelToken||a.signal)&&(d=S=>{v&&(n(!S||S.type?new Je(null,o,v):S),v.abort(),v=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const C=Za(a.url);if(C&&be.protocols.indexOf(C)===-1){n(new te("Unsupported protocol "+C+":",te.ERR_BAD_REQUEST,o));return}v.send(l||null)})},il=(o,e)=>{const{length:s}=o=o?o.filter(Boolean):[];if(e||s){let n=new AbortController,a;const l=function(u){if(!a){a=!0,h();const d=u instanceof Error?u:this.reason;n.abort(d instanceof te?d:new Je(d instanceof Error?d.message:d))}};let i=e&&setTimeout(()=>{i=null,l(new te(`timeout ${e} of ms exceeded`,te.ETIMEDOUT))},e);const h=()=>{o&&(i&&clearTimeout(i),i=null,o.forEach(u=>{u.unsubscribe?u.unsubscribe(l):u.removeEventListener("abort",l)}),o=null)};o.forEach(u=>u.addEventListener("abort",l));const{signal:g}=n;return g.unsubscribe=()=>A.asap(h),g}},cl=function*(o,e){let s=o.byteLength;if(s<e){yield o;return}let n=0,a;for(;n<s;)a=n+e,yield o.slice(n,a),n=a},dl=async function*(o,e){for await(const s of ul(o))yield*cl(s,e)},ul=async function*(o){if(o[Symbol.asyncIterator]){yield*o;return}const e=o.getReader();try{for(;;){const{done:s,value:n}=await e.read();if(s)break;yield n}}finally{await e.cancel()}},Yt=(o,e,s,n)=>{const a=dl(o,e);let l=0,i,h=g=>{i||(i=!0,n&&n(g))};return new ReadableStream({async pull(g){try{const{done:u,value:d}=await a.next();if(u){h(),g.close();return}let b=d.byteLength;if(s){let R=l+=b;s(R)}g.enqueue(new Uint8Array(d))}catch(u){throw h(u),u}},cancel(g){return h(g),a.return()}},{highWaterMark:2})},_t=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Rs=_t&&typeof ReadableStream=="function",pl=_t&&(typeof TextEncoder=="function"?(o=>e=>o.encode(e))(new TextEncoder):async o=>new Uint8Array(await new Response(o).arrayBuffer())),As=(o,...e)=>{try{return!!o(...e)}catch{return!1}},hl=Rs&&As(()=>{let o=!1;const e=new Request(be.origin,{body:new ReadableStream,method:"POST",get duplex(){return o=!0,"half"}}).headers.has("Content-Type");return o&&!e}),Qt=64*1024,Rt=Rs&&As(()=>A.isReadableStream(new Response("").body)),mt={stream:Rt&&(o=>o.body)};_t&&(o=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!mt[e]&&(mt[e]=A.isFunction(o[e])?s=>s[e]():(s,n)=>{throw new te(`Response type '${e}' is not supported`,te.ERR_NOT_SUPPORT,n)})})})(new Response);const ml=async o=>{if(o==null)return 0;if(A.isBlob(o))return o.size;if(A.isSpecCompliantForm(o))return(await new Request(be.origin,{method:"POST",body:o}).arrayBuffer()).byteLength;if(A.isArrayBufferView(o)||A.isArrayBuffer(o))return o.byteLength;if(A.isURLSearchParams(o)&&(o=o+""),A.isString(o))return(await pl(o)).byteLength},fl=async(o,e)=>{const s=A.toFiniteNumber(o.getContentLength());return s??ml(e)},vl=_t&&(async o=>{let{url:e,method:s,data:n,signal:a,cancelToken:l,timeout:i,onDownloadProgress:h,onUploadProgress:g,responseType:u,headers:d,withCredentials:b="same-origin",fetchOptions:R}=Is(o);u=u?(u+"").toLowerCase():"text";let M=il([a,l&&l.toAbortSignal()],i),_;const y=M&&M.unsubscribe&&(()=>{M.unsubscribe()});let v;try{if(g&&hl&&s!=="get"&&s!=="head"&&(v=await fl(d,n))!==0){let m=new Request(e,{method:"POST",body:n,duplex:"half"}),O;if(A.isFormData(n)&&(O=m.headers.get("content-type"))&&d.setContentType(O),m.body){const[D,z]=Jt(v,ht(Kt(g)));n=Yt(m.body,Qt,D,z)}}A.isString(b)||(b=b?"include":"omit");const $="credentials"in Request.prototype;_=new Request(e,{...R,signal:M,method:s.toUpperCase(),headers:d.normalize().toJSON(),body:n,duplex:"half",credentials:$?b:void 0});let C=await fetch(_);const S=Rt&&(u==="stream"||u==="response");if(Rt&&(h||S&&y)){const m={};["status","statusText","headers"].forEach(k=>{m[k]=C[k]});const O=A.toFiniteNumber(C.headers.get("content-length")),[D,z]=h&&Jt(O,ht(Kt(h),!0))||[];C=new Response(Yt(C.body,Qt,D,()=>{z&&z(),y&&y()}),m)}u=u||"text";let I=await mt[A.findKey(mt,u)||"text"](C,o);return!S&&y&&y(),await new Promise((m,O)=>{Es(m,O,{data:I,headers:$e.from(C.headers),status:C.status,statusText:C.statusText,config:o,request:_})})}catch($){throw y&&y(),$&&$.name==="TypeError"&&/Load failed|fetch/i.test($.message)?Object.assign(new te("Network Error",te.ERR_NETWORK,o,_),{cause:$.cause||$}):te.from($,$&&$.code,o,_)}}),At={http:Aa,xhr:rl,fetch:vl};A.forEach(At,(o,e)=>{if(o){try{Object.defineProperty(o,"name",{value:e})}catch{}Object.defineProperty(o,"adapterName",{value:e})}});const Zt=o=>`- ${o}`,gl=o=>A.isFunction(o)||o===null||o===!1,xs={getAdapter:o=>{o=A.isArray(o)?o:[o];const{length:e}=o;let s,n;const a={};for(let l=0;l<e;l++){s=o[l];let i;if(n=s,!gl(s)&&(n=At[(i=String(s)).toLowerCase()],n===void 0))throw new te(`Unknown adapter '${i}'`);if(n)break;a[i||"#"+l]=n}if(!n){const l=Object.entries(a).map(([h,g])=>`adapter ${h} `+(g===!1?"is not supported by the environment":"is not available in the build"));let i=e?l.length>1?`since :
`+l.map(Zt).join(`
`):" "+Zt(l[0]):"as no adapter specified";throw new te("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:At};function Tt(o){if(o.cancelToken&&o.cancelToken.throwIfRequested(),o.signal&&o.signal.aborted)throw new Je(null,o)}function es(o){return Tt(o),o.headers=$e.from(o.headers),o.data=kt.call(o,o.transformRequest),["post","put","patch"].indexOf(o.method)!==-1&&o.headers.setContentType("application/x-www-form-urlencoded",!1),xs.getAdapter(o.adapter||at.adapter)(o).then(function(n){return Tt(o),n.data=kt.call(o,o.transformResponse,n),n.headers=$e.from(n.headers),n},function(n){return Ps(n)||(Tt(o),n&&n.response&&(n.response.data=kt.call(o,o.transformResponse,n.response),n.response.headers=$e.from(n.response.headers))),Promise.reject(n)})}const Ds="1.9.0",$t={};["object","boolean","number","function","string","symbol"].forEach((o,e)=>{$t[o]=function(n){return typeof n===o||"a"+(e<1?"n ":" ")+o}});const ts={};$t.transitional=function(e,s,n){function a(l,i){return"[Axios v"+Ds+"] Transitional option '"+l+"'"+i+(n?". "+n:"")}return(l,i,h)=>{if(e===!1)throw new te(a(i," has been removed"+(s?" in "+s:"")),te.ERR_DEPRECATED);return s&&!ts[i]&&(ts[i]=!0,console.warn(a(i," has been deprecated since v"+s+" and will be removed in the near future"))),e?e(l,i,h):!0}};$t.spelling=function(e){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function bl(o,e,s){if(typeof o!="object")throw new te("options must be an object",te.ERR_BAD_OPTION_VALUE);const n=Object.keys(o);let a=n.length;for(;a-- >0;){const l=n[a],i=e[l];if(i){const h=o[l],g=h===void 0||i(h,l,o);if(g!==!0)throw new te("option "+l+" must be "+g,te.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new te("Unknown option "+l,te.ERR_BAD_OPTION)}}const ut={assertOptions:bl,validators:$t},Ae=ut.validators;let je=class{constructor(e){this.defaults=e||{},this.interceptors={request:new zt,response:new zt}}async request(e,s){try{return await this._request(e,s)}catch(n){if(n instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const l=a.stack?a.stack.replace(/^.+\n/,""):"";try{n.stack?l&&!String(n.stack).endsWith(l.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+l):n.stack=l}catch{}}throw n}}_request(e,s){typeof e=="string"?(s=s||{},s.url=e):s=e||{},s=Ve(this.defaults,s);const{transitional:n,paramsSerializer:a,headers:l}=s;n!==void 0&&ut.assertOptions(n,{silentJSONParsing:Ae.transitional(Ae.boolean),forcedJSONParsing:Ae.transitional(Ae.boolean),clarifyTimeoutError:Ae.transitional(Ae.boolean)},!1),a!=null&&(A.isFunction(a)?s.paramsSerializer={serialize:a}:ut.assertOptions(a,{encode:Ae.function,serialize:Ae.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),ut.assertOptions(s,{baseUrl:Ae.spelling("baseURL"),withXsrfToken:Ae.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=l&&A.merge(l.common,l[s.method]);l&&A.forEach(["delete","get","head","post","put","patch","common"],_=>{delete l[_]}),s.headers=$e.concat(i,l);const h=[];let g=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(s)===!1||(g=g&&y.synchronous,h.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let d,b=0,R;if(!g){const _=[es.bind(this),void 0];for(_.unshift.apply(_,h),_.push.apply(_,u),R=_.length,d=Promise.resolve(s);b<R;)d=d.then(_[b++],_[b++]);return d}R=h.length;let M=s;for(b=0;b<R;){const _=h[b++],y=h[b++];try{M=_(M)}catch(v){y.call(this,v);break}}try{d=es.call(this,M)}catch(_){return Promise.reject(_)}for(b=0,R=u.length;b<R;)d=d.then(u[b++],u[b++]);return d}getUri(e){e=Ve(this.defaults,e);const s=Cs(e.baseURL,e.url,e.allowAbsoluteUrls);return Ss(s,e.params,e.paramsSerializer)}};A.forEach(["delete","get","head","options"],function(e){je.prototype[e]=function(s,n){return this.request(Ve(n||{},{method:e,url:s,data:(n||{}).data}))}});A.forEach(["post","put","patch"],function(e){function s(n){return function(l,i,h){return this.request(Ve(h||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:l,data:i}))}}je.prototype[e]=s(),je.prototype[e+"Form"]=s(!0)});let yl=class Ms{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(l){s=l});const n=this;this.promise.then(a=>{if(!n._listeners)return;let l=n._listeners.length;for(;l-- >0;)n._listeners[l](a);n._listeners=null}),this.promise.then=a=>{let l;const i=new Promise(h=>{n.subscribe(h),l=h}).then(a);return i.cancel=function(){n.unsubscribe(l)},i},e(function(l,i,h){n.reason||(n.reason=new Je(l,i,h),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const s=this._listeners.indexOf(e);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const e=new AbortController,s=n=>{e.abort(n)};return this.subscribe(s),e.signal.unsubscribe=()=>this.unsubscribe(s),e.signal}static source(){let e;return{token:new Ms(function(a){e=a}),cancel:e}}};function _l(o){return function(s){return o.apply(null,s)}}function $l(o){return A.isObject(o)&&o.isAxiosError===!0}const xt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xt).forEach(([o,e])=>{xt[e]=o});function Fs(o){const e=new je(o),s=us(je.prototype.request,e);return A.extend(s,je.prototype,e,{allOwnKeys:!0}),A.extend(s,e,null,{allOwnKeys:!0}),s.create=function(a){return Fs(Ve(o,a))},s}const fe=Fs(at);fe.Axios=je;fe.CanceledError=Je;fe.CancelToken=yl;fe.isCancel=Ps;fe.VERSION=Ds;fe.toFormData=yt;fe.AxiosError=te;fe.Cancel=fe.CanceledError;fe.all=function(e){return Promise.all(e)};fe.spread=_l;fe.isAxiosError=$l;fe.mergeConfig=Ve;fe.AxiosHeaders=$e;fe.formToJSON=o=>Ts(A.isHTMLForm(o)?new FormData(o):o);fe.getAdapter=xs.getAdapter;fe.HttpStatusCode=xt;fe.default=fe;const{Axios:Zv,AxiosError:eg,CanceledError:tg,isCancel:sg,CancelToken:ng,VERSION:og,all:ag,Cancel:lg,isAxiosError:rg,spread:ig,toFormData:cg,AxiosHeaders:dg,HttpStatusCode:ug,formToJSON:pg,getAdapter:hg,mergeConfig:mg}=fe;class wl{static async getAvailableModels(){try{return(await fe({url:"/api/models/available-models",method:"GET",baseURL:""})).data}catch(e){return console.error("Failed to fetch available models:",e),{options:[],total:0}}}}const Sl={class:"config-section"},kl={class:"section-header"},Tl={key:0,class:"error-section"},Pl={class:"error-message"},El={class:"error-content"},Cl={class:"error-title"},Il={class:"error-description"},Rl={key:1,class:"visual-editor"},Al={class:"plan-basic-info"},xl={class:"form-row"},Dl={class:"form-label"},Ml=["placeholder"],Fl={key:0,class:"field-error-message"},Nl={class:"form-row"},Ul={class:"form-label"},Ll=["value","placeholder"],Bl={class:"steps-section"},Ol={class:"steps-header"},ql={class:"form-label"},jl={class:"steps-actions"},Vl=["title"],Hl={class:"steps-container"},Gl={class:"step-header"},zl={class:"step-number"},Wl={class:"step-actions"},Jl=["onClick","disabled","title"],Kl=["onClick","disabled","title"],Xl=["onClick","title"],Yl={class:"step-content"},Ql={class:"form-row"},Zl={class:"form-label"},er=["onUpdate:modelValue","placeholder"],tr={class:"form-row"},sr={class:"form-label"},nr=["onUpdate:modelValue","placeholder"],or={class:"form-row"},ar={class:"form-label"},lr={class:"model-selector"},rr=["onUpdate:modelValue","disabled"],ir={key:0,disabled:"",value:""},cr={key:1,disabled:"",value:""},dr={value:"",disabled:""},ur={value:""},pr=["value","title"],hr=["title"],mr={key:0,class:"error-message"},fr={class:"form-row"},vr={key:0,class:"empty-steps"},gr={key:0,class:"json-preview"},br={class:"preview-header"},yr={class:"form-label"},_r={class:"json-code"},$r={class:"editor-footer"},wr={class:"section-actions"},Sr=["disabled","title"],kr=["disabled","title"],Tr=["disabled"],Pr=ue({__name:"JsonEditorV2",props:{jsonContent:{},canRollback:{type:Boolean},canRestore:{type:Boolean},isGenerating:{type:Boolean},isExecuting:{type:Boolean},hiddenFields:{default:()=>[]},currentPlanTemplateId:{default:""}},emits:["rollback","restore","save","update:jsonContent"],setup(o,{emit:e}){const s=o,n=e,{showJsonPreview:a,parsedData:l,formattedJsonOutput:i,addStep:h,removeStep:g,moveStepUp:u,moveStepDown:d,handleRollback:b,handleRestore:R,handleSave:M,toggleJsonPreview:_,closeJsonPreview:y}=ds(s,n),v=N(null),$=N(""),C=N([]),S=N(!1),I=N(""),m=N(!1),O=N(-1),D=async()=>{if(!S.value){S.value=!0,I.value="";try{const E=await wl.getAvailableModels();E&&E.options?C.value=E.options:C.value=[]}catch(E){console.error("Failed to load models:",E),I.value=E instanceof Error?E.message:"Failed to load models",C.value=[]}finally{S.value=!1}}},z=E=>{O.value=E,m.value=!0,console.log("[JsonEditorV2] Available tools from store:",H.availableTools)},k=E=>{O.value>=0&&O.value<l.steps.length&&(l.steps[O.value].selectedToolKeys=[...E]),m.value=!1,O.value=-1},F=(E,x)=>{E>=0&&E<l.steps.length&&(l.steps[E].selectedToolKeys=[...x])},W=()=>{try{v.value=null,l.title||(l.title=""),l.steps||(l.steps=[]),l.directResponse=!1}catch(E){const x=`Failed to initialize JsonEditorV2: ${E instanceof Error?E.message:"Unknown error"}`;v.value=x,console.error(x,E)}};ce(()=>l,E=>{try{!E.title||!E.title.trim()?$.value="Title is required field":$.value="",v.value=null}catch(x){v.value=`Invalid data structure: ${x instanceof Error?x.message:"Unknown error"}`,$.value=""}},{immediate:!0,deep:!0}),we(()=>{W(),D()});const Q=E=>{const x=E.target;if(!x)return;x.style.height="auto";const B=20,J=Math.ceil(x.scrollHeight/B),ee=4,Z=12,he=Math.max(ee,Math.min(Z,J)),G=he*B;x.style.height=`${G}px`,x.rows=he,J>Z?x.style.overflowY="auto":x.style.overflowY="hidden"};return(E,x)=>{var B;return p(),f("div",Sl,[t("div",kl,[w(r(P),{icon:"carbon:code",width:"16"}),t("span",null,c(E.$t("sidebar.dynamicAgentPlan")),1)]),v.value?(p(),f("div",Tl,[t("div",Pl,[w(r(P),{icon:"carbon:warning",width:"16"}),t("div",El,[t("div",Cl,c(E.$t("sidebar.planTypeError")),1),t("div",Il,c(v.value),1)])])])):(p(),f("div",Rl,[t("div",Al,[t("div",xl,[t("label",Dl,c(E.$t("sidebar.title")),1),se(t("input",{"onUpdate:modelValue":x[0]||(x[0]=J=>r(l).title=J),type:"text",class:oe(["form-input",{error:$.value}]),placeholder:E.$t("sidebar.titlePlaceholder")},null,10,Ml),[[re,r(l).title]]),$.value?(p(),f("div",Fl,[w(r(P),{icon:"carbon:warning",width:"12"}),ne(" "+c($.value),1)])):L("",!0)]),t("div",Nl,[t("label",Ul,c(E.$t("sidebar.planTemplateId")),1),t("input",{value:E.currentPlanTemplateId,type:"text",class:"form-input readonly-input",readonly:"",placeholder:E.$t("sidebar.planTemplateIdPlaceholder")},null,8,Ll)])]),t("div",Bl,[t("div",Ol,[t("label",ql,c(E.$t("sidebar.tasks")),1),t("div",jl,[t("button",{onClick:x[1]||(x[1]=(...J)=>r(h)&&r(h)(...J)),class:"btn btn-xs",title:E.$t("sidebar.addStep")},[w(r(P),{icon:"carbon:add",width:"12"})],8,Vl)])]),t("div",Hl,[(p(!0),f(ae,null,ie(r(l).steps,(J,ee)=>(p(),f("div",{key:ee,class:"step-item"},[t("div",Gl,[t("span",zl,c(E.$t("sidebar.subtask"))+" "+c(ee+1),1),t("div",Wl,[t("button",{onClick:Z=>r(u)(ee),disabled:ee===0,class:"btn btn-xs",title:E.$t("sidebar.moveUp")},[w(r(P),{icon:"carbon:chevron-up",width:"12"})],8,Jl),t("button",{onClick:Z=>r(d)(ee),disabled:ee===r(l).steps.length-1,class:"btn btn-xs",title:E.$t("sidebar.moveDown")},[w(r(P),{icon:"carbon:chevron-down",width:"12"})],8,Kl),t("button",{onClick:Z=>r(g)(ee),class:"btn btn-xs btn-danger",title:E.$t("sidebar.removeStep")},[w(r(P),{icon:"carbon:trash-can",width:"12"})],8,Xl)])]),t("div",Yl,[t("div",Ql,[t("label",Zl,c(E.$t("sidebar.stepRequirement")),1),se(t("textarea",{"onUpdate:modelValue":Z=>J.stepRequirement=Z,class:"form-textarea auto-resize",placeholder:E.$t("sidebar.stepRequirementPlaceholder"),rows:"4",onInput:x[2]||(x[2]=Z=>Q(Z))},null,40,er),[[re,J.stepRequirement]])]),t("div",tr,[t("label",sr,c(E.$t("sidebar.terminateColumns")),1),se(t("textarea",{"onUpdate:modelValue":Z=>J.terminateColumns=Z,class:"form-textarea auto-resize",placeholder:E.$t("sidebar.terminateColumnsPlaceholder"),rows:"4",onInput:x[3]||(x[3]=Z=>Q(Z))},null,40,nr),[[re,J.terminateColumns]])]),t("div",or,[t("label",ar,c(E.$t("sidebar.modelName")),1),t("div",lr,[se(t("select",{"onUpdate:modelValue":Z=>J.modelName=Z,class:"form-select model-select",disabled:S.value},[S.value?(p(),f("option",ir,c(E.$t("sidebar.loading")),1)):I.value?(p(),f("option",cr,c(E.$t("sidebar.modelLoadError")),1)):L("",!0),t("option",dr,c(E.$t("sidebar.modelNameDescription")),1),t("option",ur,c(E.$t("sidebar.noModelSelected")),1),(p(!0),f(ae,null,ie(C.value,Z=>(p(),f("option",{key:Z.value,value:Z.value,title:Z.label},c(Z.label),9,pr))),128))],8,rr),[[nt,J.modelName]]),I.value?(p(),f("button",{key:0,onClick:D,class:"btn btn-sm btn-danger",title:E.$t("sidebar.retryLoadModels")},[w(r(P),{icon:"carbon:warning",width:"14"}),ne(" "+c(E.$t("sidebar.retry")),1)],8,hr)):L("",!0)]),I.value?(p(),f("div",mr,[w(r(P),{icon:"carbon:warning",width:"12"}),ne(" "+c(I.value),1)])):L("",!0)]),t("div",fr,[w(Hs,{title:E.$t("sidebar.selectedTools"),"selected-tool-ids":J.selectedToolKeys,"available-tools":r(H).availableTools,"add-button-text":E.$t("sidebar.addRemoveTools"),"empty-text":E.$t("sidebar.noTools"),"use-grid-layout":!0,onAddTools:Z=>z(ee),onToolsFiltered:Z=>F(ee,Z)},null,8,["title","selected-tool-ids","available-tools","add-button-text","empty-text","onAddTools","onToolsFiltered"])])])]))),128)),r(l).steps.length===0?(p(),f("div",vr,[w(r(P),{icon:"carbon:add-alt",width:"32",class:"empty-icon"}),t("p",null,c(E.$t("sidebar.noSteps")),1),t("button",{onClick:x[4]||(x[4]=(...J)=>r(h)&&r(h)(...J)),class:"btn btn-primary"},[w(r(P),{icon:"carbon:add",width:"14"}),ne(" "+c(E.$t("sidebar.addFirstStep")),1)])])):L("",!0)])]),r(a)?(p(),f("div",gr,[t("div",br,[t("label",yr,c(E.$t("sidebar.jsonPreview")),1),t("button",{onClick:x[5]||(x[5]=(...J)=>r(y)&&r(y)(...J)),class:"btn btn-xs"},[w(r(P),{icon:"carbon:close",width:"12"})])]),t("pre",_r,c(r(i)),1)])):L("",!0),t("div",$r,[t("button",{onClick:x[6]||(x[6]=(...J)=>r(_)&&r(_)(...J)),class:"btn btn-sm btn-secondary"},[w(r(P),{icon:"carbon:code",width:"14"}),ne(" "+c(r(a)?E.$t("sidebar.hideJson"):E.$t("sidebar.showJson")),1)]),t("div",wr,[t("button",{class:"btn btn-sm",onClick:x[7]||(x[7]=(...J)=>r(b)&&r(b)(...J)),disabled:!(E.canRollback??!1),title:E.$t("sidebar.rollback")},[w(r(P),{icon:"carbon:undo",width:"14"})],8,Sr),t("button",{class:"btn btn-sm",onClick:x[8]||(x[8]=(...J)=>r(R)&&r(R)(...J)),disabled:!(E.canRestore??!1),title:E.$t("sidebar.restore")},[w(r(P),{icon:"carbon:redo",width:"14"})],8,kr),t("button",{class:"btn btn-primary",onClick:x[9]||(x[9]=(...J)=>r(M)&&r(M)(...J)),disabled:E.isGenerating||E.isExecuting},[w(r(P),{icon:"carbon:save",width:"14"}),x[11]||(x[11]=ne(" Save "))],8,Tr)])])])),w(Gs,{modelValue:m.value,"onUpdate:modelValue":x[10]||(x[10]=J=>m.value=J),tools:r(H).availableTools,"selected-tool-ids":O.value>=0?((B=r(l).steps[O.value])==null?void 0:B.selectedToolKeys)||[]:[],onConfirm:k},null,8,["modelValue","tools","selected-tool-ids"])])}}}),Er=pe(Pr,[["__scopeId","data-v-f6cb9cef"]]),Cr={class:"config-section"},Ir={class:"section-header"},Rr={class:"execution-content"},Ar={class:"params-requirements-group"},xr={class:"params-help-text"},Dr={key:0,class:"parameter-fields"},Mr={class:"parameter-label"},Fr=["onUpdate:modelValue","placeholder","onInput"],Nr={key:0,class:"parameter-error"},Ur={key:1,class:"validation-message"},Lr=["disabled"],Br=["disabled"],Or={key:1,class:"call-example-wrapper"},qr={class:"internal-call-wrapper"},jr={class:"call-info"},Vr={class:"call-endpoint"},Hr={key:0,class:"call-endpoint"},Gr={key:2,class:"call-example-wrapper"},zr={class:"http-api-urls-wrapper"},Wr={class:"tab-container"},Jr={class:"tab-header"},Kr=["onClick"],Xr={class:"tab-content"},Yr={class:"http-api-url-display"},Qr={class:"api-method"},Zr={class:"api-endpoint"},ei={class:"api-description"},ti={key:0,class:"api-example"},si={class:"example-code"},ni={key:3,class:"call-example-wrapper"},oi=ue({__name:"ExecutionController",props:{currentPlanTemplateId:{default:""},isExecuting:{type:Boolean,default:!1},isGenerating:{type:Boolean,default:!1},showPublishButton:{type:Boolean,default:!0},toolInfo:{default:()=>({toolName:"",toolDescription:"",planTemplateId:"",inputSchema:"[]",enableHttpService:!1,enableMcpService:!1,enableInternalToolcall:!1,serviceGroup:""})}},emits:["executePlan","publishMcpService","clearParams","updateExecutionParams"],setup(o,{expose:e,emit:s}){const{t:n}=Pe(),a=o,l=s,i=N(""),h=N({parameters:[],hasParameters:!1,requirements:""}),g=N({}),u=N(!1),d=N("get-sync"),b=N({}),R=N(!1),M=N([{id:"get-sync",label:"GET + Sync",method:"GET",endpoint:"/api/executor/executeByToolNameSync/{toolName}",description:"Synchronous GET request - returns execution result immediately",example:`GET /api/executor/executeByToolNameSync/my-tool?allParams={"rawParam":"test"}
Response: {
  "status": "completed",
  "result": "Execution result here"
}`},{id:"post-sync",label:"POST + Sync",method:"POST",endpoint:"/api/executor/executeByToolNameSync",description:"Synchronous POST request - returns execution result immediately",example:`POST /api/executor/executeByToolNameSync
Content-Type: application/json

{
  "toolName": "my-tool",
  "replacementParams": {
    "rawParam": "test"
  },
  "uploadedFiles": []
}

Response: {
  "status": "completed",
  "result": "Execution result here"
}`},{id:"post-async",label:"POST + Async",method:"POST",endpoint:"/api/executor/executeByToolNameAsync",description:"Asynchronous POST request - returns task ID, check status separately",example:`POST /api/executor/executeByToolNameAsync
Content-Type: application/json

{
  "toolName": "my-tool",
  "replacementParams": {
    "rawParam": "test"
  },
  "uploadedFiles": []
}

Response: {
  "planId": "plan-123",
  "status": "processing",
  "message": "Task submitted, processing",
  "memoryId": "ABC12345",
  "toolName": "my-tool",
  "planTemplateId": "template-456"
}

# Check execution status and get detailed results:
GET /api/executor/details/{planId}
Response: {
  "currentPlanId": "plan-123",
  "title": "Plan Title",
  "status": "completed",
  "summary": "Execution completed successfully",
  "agentExecutionSequence": [...],
  "userInputWaitState": null
}`}]),_=de(()=>{var z,k,F;return((z=a.toolInfo)==null?void 0:z.enableInternalToolcall)||((k=a.toolInfo)==null?void 0:k.enableHttpService)||((F=a.toolInfo)==null?void 0:F.enableMcpService)}),y=de(()=>_.value?n("sidebar.updateServiceStatus"):n("sidebar.publishMcpService")),v=de(()=>a.isExecuting||a.isGenerating?!1:h.value.hasParameters?h.value.parameters.every(z=>{var k;return(k=g.value[z])==null?void 0:k.trim()}):!0),$=()=>{if(!O()){console.log("[ExecutionController] Parameter validation failed:",b.value);return}const z=h.value.hasParameters&&Object.keys(g.value).length>0?g.value:void 0;l("executePlan",z)},C=()=>{l("publishMcpService")},S=()=>{i.value="",l("clearParams")},I=async()=>{if(!a.currentPlanTemplateId){h.value={parameters:[],hasParameters:!1,requirements:""},g.value={};return}u.value=!0;try{const z=await Ft.getParameterRequirements(a.currentPlanTemplateId);h.value=z;const k={};z.parameters.forEach(F=>{k[F]=g.value[F]||""}),g.value=k,D()}catch(z){console.error("Failed to load parameter requirements:",z),z instanceof Error&&!z.message.includes("404")&&console.warn("Parameter requirements not available yet, will retry later"),h.value={parameters:[],hasParameters:!1,requirements:""}}finally{u.value=!1}},m=(z,k)=>{g.value[z]=k,b.value[z]&&delete b.value[z],D()},O=()=>{if(b.value={},R.value=!1,!h.value.hasParameters)return!0;let z=!1;return h.value.parameters.forEach(k=>{var W;((W=g.value[k])==null?void 0:W.trim())||(b.value[k]=`${k} is required`,z=!0)}),R.value=z,!z},D=()=>{h.value.hasParameters?i.value=JSON.stringify(g.value,null,2):i.value="",l("updateExecutionParams",i.value)};return ce(()=>a.currentPlanTemplateId,(z,k)=>{z&&z!==k&&(k&&z.startsWith("planTemplate-")?setTimeout(()=>{I()},1e3):I())}),ce(()=>i.value,z=>{l("updateExecutionParams",z)}),we(()=>{I()}),e({executionParams:i,clearExecutionParams:S,loadParameterRequirements:I}),(z,k)=>{var F,W,Q,E,x;return p(),f("div",Cr,[t("div",Ir,[w(r(P),{icon:"carbon:play",width:"16"}),t("span",null,c(r(n)("sidebar.executionController")),1)]),t("div",Rr,[t("div",Ar,[t("label",null,c(r(n)("sidebar.parameterRequirements")),1),t("div",xr,c(r(n)("sidebar.parameterRequirementsHelp")),1),h.value.hasParameters?(p(),f("div",Dr,[(p(!0),f(ae,null,ie(h.value.parameters,B=>(p(),f("div",{key:B,class:"parameter-field"},[t("label",Mr,[ne(c(B)+" ",1),k[0]||(k[0]=t("span",{class:"required"},"*",-1))]),se(t("input",{"onUpdate:modelValue":J=>g.value[B]=J,class:oe(["parameter-input",{error:b.value[B]}]),placeholder:`Enter value for ${B}`,onInput:J=>m(B,J.target.value),required:""},null,42,Fr),[[re,g.value[B]]]),b.value[B]?(p(),f("div",Nr,c(b.value[B]),1)):L("",!0)]))),128))])):L("",!0),h.value.hasParameters&&!v.value&&!a.isExecuting&&!a.isGenerating?(p(),f("div",Ur,[w(r(P),{icon:"carbon:warning",width:"14"}),ne(" "+c(r(n)("sidebar.fillAllRequiredParameters")),1)])):L("",!0)]),t("button",{class:"btn btn-primary execute-btn",onClick:$,disabled:!v.value},[w(r(P),{icon:a.isExecuting?"carbon:circle-dash":"carbon:play",width:"16",class:oe({spinning:a.isExecuting})},null,8,["icon","class"]),ne(" "+c(a.isExecuting?r(n)("sidebar.executing"):r(n)("sidebar.executePlan")),1)],8,Lr),z.showPublishButton?(p(),f("button",{key:0,class:"btn publish-mcp-btn",onClick:C,disabled:!z.currentPlanTemplateId},[w(r(P),{icon:"carbon:application",width:"16"}),ne(" "+c(y.value),1)],8,Br)):L("",!0),(F=z.toolInfo)!=null&&F.enableInternalToolcall?(p(),f("div",Or,[k[4]||(k[4]=t("div",{class:"call-example-header"},[t("h4",{class:"call-example-title"},"Internal Call"),t("p",{class:"call-example-description"},"You have published this plan-act as an internal method. You can find this tool's method in the agent configuration's add tools section and add and use it.")],-1)),t("div",qr,[t("div",jr,[k[1]||(k[1]=t("div",{class:"call-method"},"Internal Method Call",-1)),t("div",Vr,"Tool Name: "+c(((W=z.toolInfo)==null?void 0:W.toolName)||z.currentPlanTemplateId),1),(Q=z.toolInfo)!=null&&Q.serviceGroup?(p(),f("div",Hr,"Service Group: "+c(z.toolInfo.serviceGroup),1)):L("",!0),k[2]||(k[2]=t("div",{class:"call-description"},"After adding this tool in agent configuration, you can directly call this method in the agent",-1)),k[3]||(k[3]=t("div",{class:"call-example"},[t("strong",null,"Usage:"),t("pre",{class:"example-code"},`In the agent configuration's "Add Tools" section, search and add this tool, then call it directly in the agent`)],-1))])])])):L("",!0),(E=z.toolInfo)!=null&&E.enableHttpService?(p(),f("div",Gr,[k[6]||(k[6]=t("div",{class:"call-example-header"},[t("h4",{class:"call-example-title"},"HTTP Call Example"),t("p",{class:"call-example-description"},"You have published this plan-act as an HTTP service. You can call it according to the example below.")],-1)),t("div",zr,[t("div",Wr,[t("div",Jr,[(p(!0),f(ae,null,ie(M.value,B=>(p(),f("button",{key:B.id,class:oe(["tab-button",{active:d.value===B.id}]),onClick:J=>d.value=B.id},c(B.label),11,Kr))),128))]),t("div",Xr,[(p(!0),f(ae,null,ie(M.value,B=>se((p(),f("div",{key:B.id,class:"tab-panel"},[t("div",Yr,[t("div",Qr,c(B.method),1),t("div",Zr,c(B.endpoint),1),t("div",ei,c(B.description),1),B.example?(p(),f("div",ti,[k[5]||(k[5]=t("strong",null,"Example:",-1)),t("pre",si,c(B.example),1)])):L("",!0)])])),[[os,d.value===B.id]])),128))])])])])):L("",!0),(x=z.toolInfo)!=null&&x.enableMcpService?(p(),f("div",ni,k[7]||(k[7]=[ns('<div class="call-example-header" data-v-f1915ec7><h4 class="call-example-title" data-v-f1915ec7>MCP Call</h4><p class="call-example-description" data-v-f1915ec7>You have published this plan-act as an MCP service. You can use it through MCP streamable or SSE methods.</p></div><div class="mcp-call-wrapper" data-v-f1915ec7><div class="call-info" data-v-f1915ec7><div class="call-method" data-v-f1915ec7>MCP Service Call</div><div class="call-endpoint" data-v-f1915ec7>MCP Endpoint: /mcp/execute</div><div class="call-description" data-v-f1915ec7>Call through MCP protocol using streaming or SSE methods</div><div class="call-example" data-v-f1915ec7><strong data-v-f1915ec7>Usage:</strong><pre class="example-code" data-v-f1915ec7>Connect to this service through MCP client, using streamable or SSE methods for calling</pre></div></div></div>',2)]))):L("",!0)])])}}}),ai=pe(oi,[["__scopeId","data-v-f1915ec7"]]),li={class:"config-section"},ri={class:"section-header"},ii={class:"generator-content"},ci={class:"plan-type-selection"},di={class:"form-label"},ui={value:"simple"},pi={value:"dynamic_agent"},hi={key:0,class:"dynamic-agent-instruction"},mi=["value"],fi=["placeholder"],vi={class:"generator-actions"},gi=["disabled"],bi=["disabled"],yi=ue({__name:"PlanGenerator",props:{generatorPrompt:{},jsonContent:{},isGenerating:{type:Boolean},planType:{default:"dynamic_agent"}},emits:["generatePlan","updatePlan","updateGeneratorPrompt","updatePlanType"],setup(o,{expose:e,emit:s}){const{t:n}=Pe(),a=o,l=s,i=N(a.generatorPrompt),h=N(a.planType);ce(()=>a.generatorPrompt,d=>{i.value=d}),ce(i,d=>{l("updateGeneratorPrompt",d)}),ce(()=>a.planType,d=>{h.value=d}),ce(h,d=>{l("updatePlanType",d)});const g=()=>{l("generatePlan")},u=()=>{l("updatePlan")};return e({generatorPrompt:i}),(d,b)=>(p(),f("div",li,[t("div",ri,[w(r(P),{icon:"carbon:generate",width:"16"}),t("span",null,c(r(n)("sidebar.planGenerator")),1)]),t("div",ii,[t("div",ci,[t("label",di,c(r(n)("sidebar.planType")),1),se(t("select",{"onUpdate:modelValue":b[0]||(b[0]=R=>h.value=R),class:"form-select"},[t("option",ui,c(r(n)("sidebar.simplePlan")),1),t("option",pi,c(r(n)("sidebar.dynamicAgentPlan")),1)],512),[[nt,h.value]])]),h.value==="dynamic_agent"?(p(),f("div",hi,[t("textarea",{readonly:"",class:"instruction-textarea",rows:"2",value:r(n)("sidebar.dynamicAgentInstruction")},null,8,mi)])):(p(),f(ae,{key:1},[se(t("textarea",{"onUpdate:modelValue":b[1]||(b[1]=R=>i.value=R),class:"prompt-input",placeholder:r(n)("sidebar.generatorPlaceholder"),rows:"3"},null,8,fi),[[re,i.value]]),t("div",vi,[t("button",{class:"btn btn-primary btn-sm",onClick:g,disabled:d.isGenerating||!i.value.trim()},[w(r(P),{icon:d.isGenerating?"carbon:circle-dash":"carbon:generate",width:"14",class:oe({spinning:d.isGenerating})},null,8,["icon","class"]),ne(" "+c(d.isGenerating?r(n)("sidebar.generating"):r(n)("sidebar.generatePlan")),1)],8,gi),t("button",{class:"btn btn-secondary btn-sm",onClick:u,disabled:d.isGenerating||!i.value.trim()||!d.jsonContent.trim()},[w(r(P),{icon:"carbon:edit",width:"14"}),ne(" "+c(r(n)("sidebar.updatePlan")),1)],8,bi)])],64))])]))}}),_i=pe(yi,[["__scopeId","data-v-ef6f6cb4"]]),$i={class:"sidebar-content"},wi={class:"sidebar-content-header"},Si={class:"sidebar-content-title"},ki={class:"tab-switcher"},Ti=["disabled"],Pi={key:0,class:"tab-content"},Ei={class:"new-task-section"},Ci={class:"sidebar-content-list"},Ii={key:0,class:"loading-state"},Ri={key:1,class:"error-state"},Ai={key:2,class:"empty-state"},xi=["onClick"],Di={class:"task-icon"},Mi={class:"task-details"},Fi={class:"task-title"},Ni={class:"task-preview"},Ui={class:"task-time"},Li={class:"task-actions"},Bi=["title","onClick"],Oi={key:1,class:"tab-content config-tab"},qi={key:0,class:"config-container"},ji={class:"template-info-header"},Vi={class:"template-info"},Hi={class:"template-id"},Gi=["title"],zi=ue({__name:"Sidebar",emits:["planExecutionRequested"],setup(o,{expose:e,emit:s}){const{t:n}=Pe(),a=cs(),l=N(80),i=N(!1),h=N(0),g=N(0),u=N(null),d=N({toolName:"",toolDescription:"",planTemplateId:"",inputSchema:"[]",enableHttpService:!1,enableMcpService:!1,enableInternalToolcall:!1,serviceGroup:""}),b=N(null),R=N({enabled:!0,success:!0}),M=de(()=>R.value.enabled),_=async()=>{try{const T=await De.getCoordinatorToolConfig();R.value=T}catch(T){console.error("Failed to load CoordinatorTool configuration:",T),R.value={enabled:!0,success:!1,message:T instanceof Error?T.message:"Unknown error"}}},y=s,v=async()=>{try{const T=await H.saveTemplate();T!=null&&T.duplicate?a.success(n("sidebar.saveCompleted",{message:T.message,versionCount:T.versionCount})):T!=null&&T.saved?(a.success(n("sidebar.saveSuccess",{message:T.message,versionCount:T.versionCount})),$()):T!=null&&T.message&&a.success(n("sidebar.saveStatus",{message:T.message}))}catch(T){console.error("Failed to save plan modifications:",T),a.error(T.message||n("sidebar.saveFailed"))}},$=async()=>{await new Promise(T=>setTimeout(T,500)),u.value&&u.value.loadParameterRequirements(),b.value&&b.value.loadParameterRequirements()},C=async()=>{var T;try{await H.generatePlan(),a.success(n("sidebar.generateSuccess",{templateId:((T=H.selectedTemplate)==null?void 0:T.id)??n("sidebar.unknown")}))}catch(j){console.error("Failed to generate plan:",j),a.error(n("sidebar.generateFailed")+": "+j.message)}},S=async()=>{try{await H.updatePlan(),a.success(n("sidebar.updateSuccess"))}catch(T){console.error("Failed to update plan:",T),a.error(n("sidebar.updateFailed")+": "+T.message)}},I=()=>{try{H&&typeof H.rollbackVersion=="function"?H.rollbackVersion():console.warn("sidebarStore or rollbackVersion method is not available")}catch(T){console.error("Error during rollback operation:",T),a.error(n("sidebar.rollbackFailed")||"Rollback failed")}},m=()=>{try{H&&typeof H.restoreVersion=="function"?H.restoreVersion():console.warn("sidebarStore or restoreVersion method is not available")}catch(T){console.error("Error during restore operation:",T),a.error(n("sidebar.restoreFailed")||"Restore failed")}},O=async T=>{console.log("[Sidebar] handleExecutePlan called with replacementParams:",T);try{const j=H.preparePlanExecution();if(!j){console.log("[Sidebar] No plan data available, returning");return}T&&Object.keys(T).length>0&&(j.replacementParams=T,console.log("[Sidebar] Added replacement parameters to plan data:",T)),console.log("[Sidebar] Triggering plan execution request:",j),console.log("[Sidebar] Emitting planExecutionRequested event"),y("planExecutionRequested",{title:j.title,planData:j.planData,params:j.params,replacementParams:T}),console.log("[Sidebar] Event emitted")}catch(j){console.error("Error executing plan:",j),a.error(n("sidebar.executeFailed")+": "+j.message)}finally{H.finishPlanExecution()}},D=N(!1),z=()=>{if(console.log("[Sidebar] Publish MCP service button clicked"),console.log("[Sidebar] currentPlanTemplateId:",H.currentPlanTemplateId),!H.currentPlanTemplateId){console.log("[Sidebar] No plan template selected, showing warning"),a.error(n("mcpService.selectPlanTemplateFirst"));return}console.log("[Sidebar] Opening publish MCP service modal"),D.value=!0},k=T=>{T===null?(console.log("MCP service deleted successfully"),a.success(n("mcpService.deleteSuccess")),d.value={toolName:"",toolDescription:"",planTemplateId:"",inputSchema:"[]",enableHttpService:!1,enableMcpService:!1,enableInternalToolcall:!1,serviceGroup:""}):(console.log("MCP service published successfully:",T),a.success(n("mcpService.publishSuccess")),d.value={...T,toolName:T.toolName||"",serviceGroup:T.serviceGroup||""})},F=()=>{H.clearExecutionParams()},W=T=>{H.executionParams=T},Q=T=>{H.generatorPrompt=T},E=T=>{H.planType=T},x=async T=>{if(!T){d.value={toolName:"",toolDescription:"",planTemplateId:"",inputSchema:"[]",enableHttpService:!1,enableMcpService:!1,enableInternalToolcall:!1,serviceGroup:""};return}try{const j=await De.getCoordinatorToolsByTemplate(T);j?d.value={...j,toolName:j.toolName||"",serviceGroup:j.serviceGroup||""}:d.value={toolName:"",toolDescription:"",planTemplateId:T,inputSchema:"[]",enableHttpService:!1,enableMcpService:!1,enableInternalToolcall:!1,serviceGroup:""}}catch(j){console.error("Failed to load tool information:",j),d.value={toolName:"",toolDescription:"",planTemplateId:T,inputSchema:"[]",enableHttpService:!1,enableMcpService:!1,enableInternalToolcall:!1,serviceGroup:""}}},B=T=>{if(isNaN(T.getTime()))return console.warn("Invalid date received:",T),n("time.unknown");const U=new Date().getTime()-T.getTime(),q=Math.floor(U/6e4),K=Math.floor(U/36e5),ve=Math.floor(U/864e5);return q<1?n("time.now"):q<60?n("time.minuteAgo",{count:q}):K<24?n("time.hourAgo",{count:K}):ve<30?n("time.dayAgo",{count:ve}):T.toLocaleDateString("zh-CN")},J=(T,j)=>!T||T.length<=j?T:T.substring(0,j)+"...",ee=T=>{i.value=!0,h.value=T.clientX,g.value=l.value,document.addEventListener("mousemove",Z),document.addEventListener("mouseup",he),document.body.style.cursor="col-resize",document.body.style.userSelect="none",T.preventDefault()},Z=T=>{if(!i.value)return;const j=window.innerWidth,q=(T.clientX-h.value)/j*100;let K=g.value+q;K=Math.max(15,Math.min(100,K)),l.value=K},he=()=>{i.value=!1,document.removeEventListener("mousemove",Z),document.removeEventListener("mouseup",he),document.body.style.cursor="",document.body.style.userSelect="",localStorage.setItem("sidebarWidth",l.value.toString())},G=()=>{l.value=80,localStorage.setItem("sidebarWidth","80")};return ce(()=>H.currentPlanTemplateId,T=>{x(T)},{immediate:!0}),we(()=>{H.loadPlanTemplateList(),_(),H.loadAvailableTools();const T=localStorage.getItem("sidebarWidth");T&&(l.value=parseFloat(T))}),Te(()=>{document.removeEventListener("mousemove",Z),document.removeEventListener("mouseup",he)}),e({loadPlanTemplateList:H.loadPlanTemplateList,toggleSidebar:H.toggleSidebar,currentPlanTemplateId:H.currentPlanTemplateId}),(T,j)=>{var U,q;return p(),f(ae,null,[t("div",{class:oe(["sidebar-wrapper",{"sidebar-wrapper-collapsed":r(H).isCollapsed}]),style:ze({width:l.value+"%"})},[t("div",$i,[t("div",wi,[t("div",Si,c(T.$t("sidebar.title")),1)]),t("div",ki,[t("button",{class:oe(["tab-button",{active:r(H).currentTab==="list"}]),onClick:j[0]||(j[0]=K=>r(H).switchToTab("list"))},[w(r(P),{icon:"carbon:list",width:"16"}),ne(" "+c(T.$t("sidebar.templateList")),1)],2),t("button",{class:oe(["tab-button",{active:r(H).currentTab==="config"}]),onClick:j[1]||(j[1]=K=>r(H).switchToTab("config")),disabled:!r(H).selectedTemplate},[w(r(P),{icon:"carbon:settings",width:"16"}),ne(" "+c(T.$t("sidebar.configuration")),1)],10,Ti)]),r(H).currentTab==="list"?(p(),f("div",Pi,[t("div",Ei,[t("button",{class:"new-task-btn",onClick:j[2]||(j[2]=K=>r(H).createNewTemplate(r(H).planType))},[w(r(P),{icon:"carbon:add",width:"16"}),ne(" "+c(T.$t("sidebar.newPlan"))+" ",1),j[8]||(j[8]=t("span",{class:"shortcut"},"⌘ K",-1))])]),t("div",Ci,[r(H).isLoading?(p(),f("div",Ii,[w(r(P),{icon:"carbon:circle-dash",width:"20",class:"spinning"}),t("span",null,c(T.$t("sidebar.loading")),1)])):r(H).errorMessage?(p(),f("div",Ri,[w(r(P),{icon:"carbon:warning",width:"20"}),t("span",null,c(r(H).errorMessage),1),t("button",{onClick:j[3]||(j[3]=(...K)=>r(H).loadPlanTemplateList&&r(H).loadPlanTemplateList(...K)),class:"retry-btn"},c(T.$t("sidebar.retry")),1)])):r(H).planTemplateList.length===0?(p(),f("div",Ai,[w(r(P),{icon:"carbon:document",width:"32"}),t("span",null,c(T.$t("sidebar.noTemplates")),1)])):(p(!0),f(ae,{key:3},ie(r(H).sortedTemplates,K=>(p(),f("div",{key:K.id,class:oe(["sidebar-content-list-item",{"sidebar-content-list-item-active":K.id===r(H).currentPlanTemplateId}]),onClick:ve=>r(H).selectTemplate(K)},[t("div",Di,[w(r(P),{icon:"carbon:document",width:"20"})]),t("div",Mi,[t("div",Fi,c(K.title||T.$t("sidebar.unnamedPlan")),1),t("div",Ni,c(J(K.description||T.$t("sidebar.noDescription"),40)),1)]),t("div",Ui,c(B(r(H).parseDateTime(K.updateTime||K.createTime))),1),t("div",Li,[t("button",{class:"delete-task-btn",title:T.$t("sidebar.deleteTemplate"),onClick:me(ve=>r(H).deleteTemplate(K),["stop"])},[w(r(P),{icon:"carbon:close",width:"16"})],8,Bi)])],10,xi))),128))])])):r(H).currentTab==="config"?(p(),f("div",Oi,[r(H).selectedTemplate?(p(),f("div",qi,[t("div",ji,[t("div",Vi,[t("h3",null,c(r(H).selectedTemplate.title||T.$t("sidebar.unnamedPlan")),1),t("span",Hi,"ID: "+c(r(H).selectedTemplate.id),1)]),t("button",{class:"back-to-list-btn",onClick:j[4]||(j[4]=K=>r(H).switchToTab("list"))},[w(r(P),{icon:"carbon:arrow-left",width:"16"})])]),w(_i,{"generator-prompt":r(H).generatorPrompt,"json-content":r(H).jsonContent,"is-generating":r(H).isGenerating,"plan-type":r(H).planType,onGeneratePlan:C,onUpdatePlan:S,onUpdateGeneratorPrompt:Q,onUpdatePlanType:E},null,8,["generator-prompt","json-content","is-generating","plan-type"]),r(H).planType==="dynamic_agent"?(p(),le(Er,{key:0,"json-content":r(H).jsonContent,"can-rollback":r(H).canRollback,"can-restore":r(H).canRestore,"is-generating":r(H).isGenerating,"is-executing":r(H).isExecuting,"current-plan-template-id":r(H).currentPlanTemplateId||"",onRollback:I,onRestore:m,onSave:v,"onUpdate:jsonContent":j[5]||(j[5]=K=>r(H).jsonContent=K)},null,8,["json-content","can-rollback","can-restore","is-generating","is-executing","current-plan-template-id"])):(p(),le(Go,{key:1,"json-content":r(H).jsonContent,"can-rollback":r(H).canRollback,"can-restore":r(H).canRestore,"is-generating":r(H).isGenerating,"is-executing":r(H).isExecuting,"current-plan-template-id":r(H).currentPlanTemplateId||"",onRollback:I,onRestore:m,onSave:v,"onUpdate:jsonContent":j[6]||(j[6]=K=>r(H).jsonContent=K)},null,8,["json-content","can-rollback","can-restore","is-generating","is-executing","current-plan-template-id"])),w(ai,{ref_key:"executionControllerRef",ref:u,"current-plan-template-id":r(H).currentPlanTemplateId||"","is-executing":r(H).isExecuting,"is-generating":r(H).isGenerating,"show-publish-button":M.value,"tool-info":d.value,onExecutePlan:O,onPublishMcpService:z,onClearParams:F,onUpdateExecutionParams:W},null,8,["current-plan-template-id","is-executing","is-generating","show-publish-button","tool-info"])])):L("",!0)])):L("",!0)]),t("div",{class:"sidebar-resizer",onMousedown:ee,onDblclick:G,title:T.$t("sidebar.resizeHint")},j[9]||(j[9]=[t("div",{class:"resizer-line"},null,-1)]),40,Gi)],6),w(Zn,{ref_key:"publishMcpModalRef",ref:b,modelValue:D.value,"onUpdate:modelValue":j[7]||(j[7]=K=>D.value=K),"plan-template-id":r(H).currentPlanTemplateId||"","plan-title":((U=r(H).selectedTemplate)==null?void 0:U.title)||"","plan-description":((q=r(H).selectedTemplate)==null?void 0:q.description)||"",onPublished:k},null,8,["modelValue","plan-template-id","plan-title","plan-description"])],64)}}}),Wi=pe(zi,[["__scopeId","data-v-7b9b059f"]]);class pt{static async handleResponse(e){if(!e.ok)try{const s=await e.json();throw new Error(s.message||`API request failed: ${e.status}`)}catch{throw new Error(`API request failed: ${e.status} ${e.statusText}`)}return e}static async getMemories(){try{const e=await fetch(`${this.BASE_URL}`);return await(await this.handleResponse(e)).json()}catch(e){throw console.error("Failed to get memory list:",e),e}}static async getMemory(e){try{const s=await fetch(`${this.BASE_URL}/single?memoryId=${e}`);return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to get memory :",s),s}}static async update(e,s){try{const n=await fetch(`${this.BASE_URL}/update`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({memoryId:e,memoryName:s})});return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to update memory:",n),n}}static async delete(e){try{const s=await fetch(`${this.BASE_URL}/delete?memoryId=${e}`);await this.handleResponse(s)}catch(s){throw console.error("Failed to update memory:",s),s}}}ge(pt,"BASE_URL","/api/memories");class Ji{constructor(){ge(this,"isCollapsed",!1);ge(this,"selectMemoryId","");ge(this,"loadMessages",()=>{});ge(this,"intervalId")}toggleSidebar(){this.isCollapsed=!this.isCollapsed,this.isCollapsed?(this.loadMessages(),this.intervalId=window.setInterval(()=>{this.loadMessages()},3e3)):clearInterval(this.intervalId)}selectMemory(e){this.toggleSidebar(),this.selectMemoryId=e}setMemory(e){this.selectMemoryId=e}defaultMemoryId(){this.selectMemoryId=this.generateRandomId()}clearMemoryId(){this.selectMemoryId=""}generateRandomId(){return Math.random().toString(36).substring(2,10)}setLoadMessages(e){this.loadMessages=e}}const Ne=st(new Ji),Ki={key:0,class:"app-container"},Xi={class:"app-content"},Yi={class:"header"},Qi={class:"relative"},Zi={class:"title-edit-group"},ec={id:"main-title",class:"main-title"},tc={key:0,id:"title-edit-container",class:"title-edit-container"},sc={class:"search-bar"},nc={class:"search-container"},oc=["placeholder"],ac={class:"message-list",id:"message-list"},lc={class:"message-header"},rc={class:"message-content"},ic={class:"sender-info"},cc=["onClick"],dc={class:"sender-name"},uc=["onClick"],pc={class:"action-buttons"},hc=["onClick"],mc={class:"action-buttons"},fc=["onClick"],vc={class:"message-preview"},gc={class:"preview-line"},bc={key:0,class:"preview-line",style:{opacity:"0.8"}},yc={class:"message-meta"},_c={class:"message-id"},$c={class:"meta-right"},wc={key:0,class:"unread-count"},Sc={class:"message-time"},kc=["id"],Tc={style:{display:"flex","flex-direction":"column",gap:"0.75rem"}},Pc={class:"bubble-avatar"},Ec={class:"bubble-content"},Cc={class:"bubble-text"},Ic={key:0,class:"empty-state"},Rc={class:"modal-content"},Ac={class:"modal-header"},xc={class:"modal-title"},Dc=["placeholder"],Mc={id:"name-char-count",class:"char-count",style:{"text-align":"right",display:"block","margin-top":"0.25rem"}},Fc={class:"modal-footer"},Nc={class:"modal-content"},Uc={class:"modal-header"},Lc={class:"modal-title"},Bc={class:"state-text",id:"delete-message"},Oc={class:"modal-footer"},qc=ue({__name:"Memory",emits:["memory-selected"],setup(o,{emit:e}){const s=N(!1),n=N(""),a=N([]),l=N([]),i=N(!1),h=N(null),g=N(""),u=N(!1),d=N(null),b=new Map,R=e,M=k=>{k.key==="Escape"&&(u.value?D():i.value&&S())};we(()=>{Ne.setLoadMessages(_),document.addEventListener("keydown",M)}),Te(()=>{document.removeEventListener("keydown",M)});const _=async()=>{try{const k=await pt.getMemories();a.value?a.value=k.map(F=>({...F,expanded:b.has(F.memoryId)?b.get(F.memoryId):!1})):a.value=k.map(F=>({...F,expanded:!1})),l.value=[...a.value],$()}catch(k){console.error("error:",k),a.value=[],l.value=[]}},y=k=>{Ne.selectMemory(k),R("memory-selected")},v=k=>{const F=typeof k=="string"?parseInt(k,10):k;return isNaN(F)||F<=0?"unknow time":new Date(F.toString().length===13?F:F*1e3).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(","," ")},$=()=>{const k=n.value.toLowerCase().trim();if(!k){l.value=[...a.value];return}l.value=a.value.filter(F=>{const W=F.memoryName.toLowerCase().includes(k),Q=F.memoryId.toLowerCase().includes(k),E=F.messages.some(x=>x.text.toLowerCase().includes(k));return W||Q||E})},C=(k,F)=>{h.value=k,g.value=F,i.value=!0},S=()=>{i.value=!1,h.value=null},I=async()=>{if(!h.value)return;const k=g.value.trim()||"unknow name",F=a.value.findIndex(W=>W.memoryId===h.value);if(F!==-1){const W=a.value[F];try{const Q=await pt.update(W.memoryId,k);Q.messages||(Q.messages=[]),a.value[F]={...Q,expanded:W.expanded},$(),i.value=!1}catch(Q){console.error("error:",Q)}}},m=k=>{const F=a.value.find(W=>W.memoryId===k);if(F){F.expanded=!F.expanded,b.set(k,F.expanded);const W=l.value.findIndex(Q=>Q.memoryId===k);W!==-1&&(l.value[W]={...F})}},O=k=>{d.value=k,u.value=!0},D=()=>{u.value=!1,d.value=null},z=async()=>{if(d.value)try{await pt.delete(d.value),a.value=a.value.filter(k=>k.memoryId!==d.value),$(),a.value.length===0&&Ne.clearMemoryId(),u.value=!1,d.value=null}catch(k){console.error("error:",k)}};return(k,F)=>(p(),le(Qe,{to:"body"},[w(qe,{name:"modal"},{default:Me(()=>[r(Ne).isCollapsed?(p(),f("div",Ki,[t("div",Xi,[t("div",Yi,[t("div",Qi,[t("div",Zi,[t("h1",ec,[t("span",null,c(k.$t("memory.title")),1)])]),s.value?(p(),f("div",tc)):L("",!0)]),t("button",{class:"close-btn",onClick:F[0]||(F[0]=W=>r(Ne).toggleSidebar())},[w(r(P),{icon:"carbon:close"})])]),t("div",sc,[t("div",nc,[w(r(P),{class:"search-container-icon",icon:"carbon:search"}),se(t("input",{type:"text",placeholder:k.$t("memory.searchPlaceholder"),class:"search-input","onUpdate:modelValue":F[1]||(F[1]=W=>n.value=W),onInput:$},null,40,oc),[[re,n.value]])])]),t("div",ac,[t("div",null,[(p(!0),f(ae,null,ie(l.value,W=>(p(),f("div",{class:oe(["message-item",{expanded:W.expanded}]),key:W.memoryId},[t("div",lc,[t("div",rc,[t("div",ic,[t("div",{class:"sender-div",onClick:me(Q=>y(W.memoryId),["stop"])},[t("h3",dc,c(W.memoryName),1)],8,cc),t("div",{class:"toggle-container",onClick:me(Q=>C(W.memoryId,W.memoryName),["stop"])},[w(r(P),{icon:"carbon:edit",class:"edit-btn"})],8,uc),t("div",pc,[t("button",{class:"delete-btn",onClick:me(Q=>m(W.memoryId),["stop"])},[w(r(P),{id:"toggle-"+W.memoryId,icon:"carbon:chevron-down",class:"down-btn"},null,8,["id"])],8,hc)]),t("div",mc,[t("button",{class:"delete-btn",onClick:me(Q=>O(W.memoryId),["stop"])},[w(r(P),{icon:"carbon:delete"})],8,fc)])]),t("div",vc,[t("p",gc,c(W.messages.length>0?W.messages[0].text:"none message"),1),W.messages.length>1?(p(),f("p",bc,c(W.messages[1].text),1)):L("",!0)]),t("div",yc,[t("span",_c,"ID: "+c(W.memoryId),1),t("div",$c,[W.messages.length>0?(p(),f("span",wc,c(W.messages.length)+" "+c(k.$t("memory.size")),1)):L("",!0),t("span",Sc,c(v(W.createTime)),1)])])])]),W.expanded?(p(),f("div",{key:0,id:"content-"+W.memoryId,class:"expanded-content"},[t("div",Tc,[(p(!0),f(ae,null,ie(W.messages,(Q,E)=>(p(),f("div",{class:"message-bubble",key:E},[t("div",Pc,c(Q.messageType),1),t("div",Ec,[t("p",Cc,c(Q.text),1)])]))),128))])],8,kc)):L("",!0)],2))),128))]),l.value.length===0&&n.value?(p(),f("div",Ic,F[3]||(F[3]=[t("p",{class:"state-text"},"none message",-1)]))):L("",!0)]),i.value?(p(),f("div",{key:0,id:"name-edit-modal",class:"modal-overlay",onClick:me(S,["self"])},[t("div",Rc,[t("div",Ac,[t("h3",xc,c(k.$t("memory.changeName")),1),se(t("input",{type:"text","onUpdate:modelValue":F[2]||(F[2]=W=>g.value=W),class:"edit-input",placeholder:k.$t("memory.newNamePlaceholder")},null,8,Dc),[[re,g.value]]),t("span",Mc,c(g.value.length),1)]),t("div",Fc,[t("button",{id:"cancel-name",class:"modal-btn cancel-btn",onClick:S},c(k.$t("memory.cancel")),1),t("button",{id:"save-name",class:"modal-btn confirm-btn",onClick:I},c(k.$t("memory.save")),1)])])])):L("",!0),u.value?(p(),f("div",{key:1,id:"delete-modal",class:"modal-overlay",onClick:me(D,["self"])},[t("div",Nc,[t("div",Uc,[t("h3",Lc,c(k.$t("memory.deleteHint")),1),t("p",Bc,c(k.$t("memory.deleteHintPrefix"))+" "+c(d.value)+" "+c(k.$t("memory.deleteHintSuffix")),1)]),t("div",Oc,[t("button",{id:"cancel-delete",class:"modal-btn cancel-btn",onClick:D},c(k.$t("memory.cancel")),1),t("button",{id:"confirm-delete",class:"modal-btn delete-btn-confirm",onClick:z},c(k.$t("memory.delete")),1)])])])):L("",!0)])])):L("",!0)]),_:1})]))}}),jc=pe(qc,[["__scopeId","data-v-e212f55a"]]),Vc="/api/executor";async function Ns(o){try{const e=await fetch(`${Vc}/agent-execution/${o}`);if(!e.ok){if(e.status===404)return console.warn(`Agent execution detail not found for stepId: ${o}`),null;throw new Error(`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){return console.error(`Error fetching agent execution detail for stepId: ${o}:`,e),null}}async function Hc(o){return console.log(`Refreshing agent execution detail for stepId: ${o}`),Ns(o)}class Le{static async handleResponse(e){if(!e.ok)try{const s=await e.json();throw new Error(s.message||`API request failed: ${e.status}`)}catch{throw new Error(`API request failed: ${e.status} ${e.statusText}`)}return e}static async getFileTree(e){try{const s=await fetch(`${this.BASE_URL}/tree/${e}`),a=await(await this.handleResponse(s)).json();if(!a.success)throw new Error(a.message||"Failed to get file tree");return a.data}catch(s){throw console.error("Failed to get file tree:",s),s}}static async getFileContent(e,s){try{const n=await fetch(`${this.BASE_URL}/content/${e}?path=${encodeURIComponent(s)}`),l=await(await this.handleResponse(n)).json();if(!l.success)throw new Error(l.message||"Failed to get file content");return l.data}catch(n){throw console.error("Failed to get file content:",n),n}}static async downloadFile(e,s,n){try{const a=await fetch(`${this.BASE_URL}/download/${e}?path=${encodeURIComponent(s)}`);await this.handleResponse(a);const l=await a.blob(),i=window.URL.createObjectURL(l),h=document.createElement("a");h.href=i,h.download=n||s.split("/").pop()||"download",document.body.appendChild(h),h.click(),window.URL.revokeObjectURL(i),document.body.removeChild(h)}catch(a){throw console.error("Failed to download file:",a),a}}static isTextFile(e,s){const n=["text/","application/json","application/xml","application/javascript","application/typescript"],a=[".txt",".md",".json",".xml",".html",".css",".js",".ts",".vue",".jsx",".tsx",".py",".java",".cpp",".c",".h",".sh",".bat",".yml",".yaml",".properties",".conf",".cfg"];if(n.some(i=>e.startsWith(i)))return!0;const l=s.toLowerCase();return a.some(i=>l.endsWith(i))}static getFileIcon(e){if(e.type==="directory")return"carbon:folder";const s=e.name.toLowerCase();return s.endsWith(".js")?"vscode-icons:file-type-js":s.endsWith(".ts")?"vscode-icons:file-type-typescript":s.endsWith(".vue")?"vscode-icons:file-type-vue":s.endsWith(".java")?"vscode-icons:file-type-java":s.endsWith(".py")?"vscode-icons:file-type-python":s.endsWith(".json")?"vscode-icons:file-type-json":s.endsWith(".xml")?"vscode-icons:file-type-xml":s.endsWith(".html")?"vscode-icons:file-type-html":s.endsWith(".css")?"vscode-icons:file-type-css":s.endsWith(".md")?"vscode-icons:file-type-markdown":s.endsWith(".yml")||s.endsWith(".yaml")?"vscode-icons:file-type-yaml":s.endsWith(".pdf")?"vscode-icons:file-type-pdf2":s.endsWith(".doc")||s.endsWith(".docx")?"vscode-icons:file-type-word":s.endsWith(".xls")||s.endsWith(".xlsx")?"vscode-icons:file-type-excel":s.endsWith(".ppt")||s.endsWith(".pptx")?"vscode-icons:file-type-powerpoint":s.match(/\.(jpg|jpeg|png|gif|bmp|svg)$/)?"carbon:image":s.match(/\.(zip|rar|7z|tar|gz)$/)?"carbon:archive":"carbon:document"}}ge(Le,"BASE_URL","/api/file-browser");const Gc={class:"file-tree-node"},zc={class:"node-icon"},Wc={class:"node-name"},Jc={key:1,class:"file-size"},Kc={key:2,class:"node-actions"},Xc={key:0,class:"children"},Yc=ue({__name:"FileTreeNode",props:{node:{},level:{}},emits:["file-selected","download-file"],setup(o,{emit:e}){const s=o,n=e,a=N(s.level===0),l=N(!1),i=N(!1),h=N({}),g=()=>{s.node.type==="directory"&&(a.value=!a.value)},u=()=>{s.node.type==="directory"?g():d()},d=()=>{s.node.type==="file"&&n("file-selected",s.node),M()},b=()=>{n("download-file",s.node),M()},R=$=>{$.preventDefault(),i.value=!0,h.value={position:"fixed",top:`${$.clientY}px`,left:`${$.clientX}px`,zIndex:1e3},setTimeout(()=>{document.addEventListener("click",M)},0)},M=()=>{i.value=!1,document.removeEventListener("click",M)},_=async()=>{try{await navigator.clipboard.writeText(s.node.path)}catch($){console.error("Failed to copy path:",$)}M()},y=()=>s.node.type==="directory"?a.value?"carbon:folder-open":"carbon:folder":Le.getFileIcon(s.node),v=$=>{if($===0)return"0 B";const C=1024,S=["B","KB","MB","GB"],I=Math.floor(Math.log($)/Math.log(C));return parseFloat(($/Math.pow(C,I)).toFixed(1))+" "+S[I]};return Te(()=>{document.removeEventListener("click",M)}),($,C)=>{const S=as("FileTreeNode",!0);return p(),f("div",Gc,[t("div",{class:oe(["node-content",{"is-directory":$.node.type==="directory","is-file":$.node.type==="file","is-expanded":a.value}]),style:ze({paddingLeft:`${$.level*16+12}px`}),onClick:u,onContextmenu:me(R,["prevent"])},[$.node.type==="directory"?(p(),f("div",{key:0,class:"expand-icon",onClick:me(g,["stop"])},[w(r(P),{icon:a.value?"carbon:chevron-down":"carbon:chevron-right",class:"chevron-icon"},null,8,["icon"])])):L("",!0),t("div",zc,[w(r(P),{icon:y()},null,8,["icon"])]),t("span",Wc,c($.node.name),1),$.node.type==="file"?(p(),f("span",Jc,c(v($.node.size)),1)):L("",!0),l.value?(p(),f("div",Kc,[$.node.type==="file"?(p(),f("button",{key:0,onClick:C[0]||(C[0]=me(I=>$.$emit("download-file",$.node),["stop"])),class:"action-btn download-btn",title:"Download"},[w(r(P),{icon:"carbon:download"})])):L("",!0)])):L("",!0)],38),$.node.type==="directory"&&a.value&&$.node.children?(p(),f("div",Xc,[(p(!0),f(ae,null,ie($.node.children,I=>(p(),le(S,{key:I.path,node:I,level:$.level+1,onFileSelected:C[1]||(C[1]=m=>$.$emit("file-selected",m)),onDownloadFile:C[2]||(C[2]=m=>$.$emit("download-file",m))},null,8,["node","level"]))),128))])):L("",!0),i.value?(p(),f("div",{key:1,class:"context-menu",style:ze(h.value),onClick:C[3]||(C[3]=me(()=>{},["stop"]))},[t("div",{class:"context-menu-item",onClick:d},[w(r(P),{icon:"carbon:view"}),C[4]||(C[4]=t("span",null,"Open",-1))]),$.node.type==="file"?(p(),f("div",{key:0,class:"context-menu-item",onClick:b},[w(r(P),{icon:"carbon:download"}),C[5]||(C[5]=t("span",null,"Download",-1))])):L("",!0),C[7]||(C[7]=t("div",{class:"context-menu-divider"},null,-1)),t("div",{class:"context-menu-item",onClick:_},[w(r(P),{icon:"carbon:copy"}),C[6]||(C[6]=t("span",null,"Copy Path",-1))])],4)):L("",!0)])}}}),Qc=pe(Yc,[["__scopeId","data-v-e4a376d5"]]),Zc={class:"file-browser"},ed={class:"file-browser-header"},td={class:"header-actions"},sd=["disabled","title"],nd={class:"file-browser-content"},od={class:"file-tree-panel"},ad={key:0,class:"loading-state"},ld={key:1,class:"error-state"},rd={key:0,class:"waiting-for-files"},id={class:"message-content"},cd={class:"tips"},dd=["disabled"],ud={key:1,class:"actual-error"},pd={key:2,class:"empty-state"},hd={key:3,class:"file-tree"},md={key:0,class:"file-content-panel"},fd={class:"file-content-header"},vd={class:"file-info"},gd={class:"file-name"},bd={class:"file-size"},yd={class:"file-actions"},_d=["title"],$d=["title"],wd={class:"file-content-body"},Sd={key:0,class:"loading-content"},kd={key:1,class:"content-error"},Td={key:2,class:"file-content"},Pd={key:0,class:"text-content"},Ed={key:1,class:"binary-content"},Cd=ue({__name:"index",props:{planId:{}},setup(o){const e=o,{t:s}=Pe(),n=N(!1),a=N(null),l=N(null),i=N(null),h=N(null),g=N(!1),u=N(null),d=N(null),b=de(()=>!h.value||!i.value?!1:Le.isTextFile(h.value.mimeType,i.value.name)),R=de(()=>a.value&&(a.value.includes("Plan directory not found")||a.value.includes("not found"))),M=()=>{d.value&&(clearTimeout(d.value),d.value=null)},_=()=>{M(),d.value=window.setTimeout(()=>{R.value&&y()},5e3)},y=async()=>{if(e.planId){n.value=!0,a.value=null,M();try{l.value=await Le.getFileTree(e.planId)}catch(m){a.value=m instanceof Error?m.message:s("fileBrowser.loadError"),console.error("Failed to load file tree:",m);const O=m instanceof Error?m.message:"";(O.includes("Plan directory not found")||O.includes("not found"))&&_()}finally{n.value=!1}}},v=async m=>{if(m.type!=="directory"){i.value=m,h.value=null,g.value=!0,u.value=null;try{h.value=await Le.getFileContent(e.planId,m.path)}catch(O){u.value=O instanceof Error?O.message:s("fileBrowser.contentLoadError"),console.error("Failed to load file content:",O)}finally{g.value=!1}}},$=async m=>{try{await Le.downloadFile(e.planId,m.path,m.name)}catch(O){console.error("Failed to download file:",O)}},C=()=>{i.value=null,h.value=null,u.value=null},S=m=>Le.getFileIcon(m),I=m=>{if(m===0)return"0 B";const O=1024,D=["B","KB","MB","GB"],z=Math.floor(Math.log(m)/Math.log(O));return parseFloat((m/Math.pow(O,z)).toFixed(1))+" "+D[z]};return ce(()=>e.planId,m=>{m&&(i.value=null,h.value=null,y())},{immediate:!0}),we(()=>{e.planId&&y()}),Te(()=>{M()}),(m,O)=>(p(),f("div",Zc,[t("div",ed,[t("h3",null,c(m.$t("fileBrowser.title")),1),t("div",td,[t("button",{class:"refresh-btn",onClick:y,disabled:n.value,title:m.$t("fileBrowser.refresh")},[w(r(P),{icon:"carbon:refresh",class:oe({rotating:n.value}),style:{color:"#ffffff",fontSize:"18px",width:"18px",height:"18px"}},null,8,["class"])],8,sd)])]),t("div",nd,[t("div",od,[n.value?(p(),f("div",ad,[w(r(P),{icon:"carbon:loading",class:"rotating"}),t("span",null,c(m.$t("fileBrowser.loading")),1)])):a.value?(p(),f("div",ld,[R.value?(p(),f("div",rd,[w(r(P),{icon:"carbon:time",class:"rotating"}),t("div",id,[t("h3",null,c(m.$t("fileBrowser.waitingForGeneration")),1),t("p",null,c(m.$t("fileBrowser.planExecuting")),1),t("div",cd,[w(r(P),{icon:"carbon:information"}),t("span",null,c(m.$t("fileBrowser.filesTip")),1)])]),t("button",{onClick:y,class:"retry-btn",disabled:n.value},[w(r(P),{icon:"carbon:refresh",class:oe({rotating:n.value})},null,8,["class"]),ne(" "+c(n.value?m.$t("fileBrowser.checking"):m.$t("fileBrowser.checkNow")),1)],8,dd)])):(p(),f("div",ud,[w(r(P),{icon:"carbon:warning"}),t("span",null,c(a.value),1),t("button",{onClick:y,class:"retry-btn"},c(m.$t("fileBrowser.retry")),1)]))])):l.value?(p(),f("div",hd,[w(Qc,{node:l.value,level:0,onFileSelected:v,onDownloadFile:$},null,8,["node"])])):(p(),f("div",pd,[w(r(P),{icon:"carbon:folder-off"}),t("span",null,c(m.$t("fileBrowser.noFiles")),1)]))]),i.value?(p(),f("div",md,[t("div",fd,[t("div",vd,[w(r(P),{icon:S(i.value)},null,8,["icon"]),t("span",gd,c(i.value.name),1),t("span",bd,"("+c(I(i.value.size))+")",1)]),t("div",yd,[t("button",{onClick:O[0]||(O[0]=D=>$(i.value)),class:"download-btn",title:m.$t("fileBrowser.download")},[w(r(P),{icon:"carbon:download"})],8,_d),t("button",{onClick:C,class:"close-btn",title:m.$t("common.close")},[w(r(P),{icon:"carbon:close"})],8,$d)])]),t("div",wd,[g.value?(p(),f("div",Sd,[w(r(P),{icon:"carbon:loading",class:"rotating"}),t("span",null,c(m.$t("fileBrowser.loadingContent")),1)])):u.value?(p(),f("div",kd,[w(r(P),{icon:"carbon:warning"}),t("span",null,c(u.value),1)])):h.value?(p(),f("div",Td,[b.value?(p(),f("div",Pd,[t("pre",null,[t("code",null,c(h.value.content),1)])])):(p(),f("div",Ed,[w(r(P),{icon:"carbon:document-unknown"}),t("p",null,c(m.$t("fileBrowser.binaryFile")),1),t("button",{onClick:O[1]||(O[1]=D=>$(i.value)),class:"download-btn-large"},[w(r(P),{icon:"carbon:download"}),ne(" "+c(m.$t("fileBrowser.downloadToView")),1)])]))])):L("",!0)])])):L("",!0)])]))}}),Id=pe(Cd,[["__scopeId","data-v-e7c74fe4"]]),Rd={class:"right-panel"},Ad={class:"preview-header"},xd={class:"preview-tabs"},Dd={class:"preview-content"},Md={key:0,class:"step-details"},Fd={key:0,class:"step-info"},Nd={key:0,class:"agent-info"},Ud={class:"info-item"},Ld={class:"label"},Bd={class:"value"},Od={class:"info-item"},qd={class:"label"},jd={class:"value"},Vd={class:"info-item"},Hd={class:"label"},Gd={class:"execution-status"},zd={class:"status-item"},Wd={class:"status-text"},Jd={key:0},Kd={key:0,class:"think-act-steps"},Xd={class:"steps-container"},Yd={class:"step-header"},Qd={class:"step-number"},Zd={class:"think-section"},eu={class:"think-content"},tu={class:"input"},su={class:"label"},nu={class:"output"},ou={class:"label"},au={key:0,class:"action-section"},lu={class:"action-content"},ru={class:"tool-info"},iu={class:"label"},cu={class:"value"},du={class:"input"},uu={class:"label"},pu={class:"output"},hu={class:"label"},mu={key:0,class:"sub-plan-section"},fu={class:"sub-plan-content"},vu={class:"sub-plan-header"},gu={class:"sub-plan-info"},bu={class:"label"},yu={class:"value"},_u={key:0,class:"sub-plan-info"},$u={class:"label"},wu={class:"value"},Su={class:"sub-plan-status"},ku={class:"status-text"},Tu={key:0,class:"no-steps-message"},Pu={key:1,class:"no-execution-message"},Eu={class:"step-basic-info"},Cu={class:"info-item"},Iu={class:"label"},Ru={class:"value"},Au={key:0,class:"info-item"},xu={class:"label"},Du={class:"value"},Mu={class:"info-item"},Fu={class:"label"},Nu={class:"no-execution-hint"},Uu={key:2,class:"execution-indicator"},Lu={class:"execution-text"},Bu={key:1,class:"no-selection"},Ou=["title"],qu={key:1,class:"file-browser-container"},ju={key:1,class:"no-plan-message"},Vu={class:"message-content"},Hu={class:"tips"},Gu=ue({__name:"RightPanel",props:{currentRootPlanId:{},selectedStepId:{}},setup(o,{expose:e}){const s=o,{t:n}=Pe(),a=N(),l=N(),i=N("details"),h=N(localStorage.getItem("jmanus-last-plan-id")),g=N(localStorage.getItem("jmanus-has-executed-plan")==="true"),u=N(!1),d=N(!0),b=N(!0),R=de(()=>l.value?l.value.completed?n("rightPanel.status.completed"):l.value.current?n("rightPanel.status.executing"):n("rightPanel.status.waiting"):""),M=de(()=>s.currentRootPlanId?s.currentRootPlanId:h.value),_=de(()=>!M.value&&!g.value),y=async k=>{var F,W,Q;if(console.log("[RightPanel] Step selected:",{stepId:k}),!k){console.warn("[RightPanel] No stepId provided"),l.value=null;return}try{const E=await Ns(k);if(!E){console.warn("[RightPanel] Agent execution detail not found for stepId:",k),l.value=null;return}const x={stepId:k,title:E.agentName||`Step ${k}`,description:E.agentDescription||"",agentExecution:E,completed:E.status==="FINISHED",current:E.status==="RUNNING"};l.value=x,console.log("[RightPanel] Step details updated:",x),console.log("[RightPanel] activeTab:",i.value),console.log("[RightPanel] selectedStep.value:",l.value),console.log("[RightPanel] agentExecution:",l.value.agentExecution),console.log("[RightPanel] thinkActSteps:",(F=l.value.agentExecution)==null?void 0:F.thinkActSteps),console.log("[RightPanel] thinkActSteps length:",(Q=(W=l.value.agentExecution)==null?void 0:W.thinkActSteps)==null?void 0:Q.length),await ke(),console.log("[RightPanel] After nextTick - selectedStep:",l.value),setTimeout(()=>{C()},100),I()}catch(E){console.error("[RightPanel] Error fetching step details:",E),l.value=null}},v=async()=>{var k;if(!((k=l.value)!=null&&k.stepId)){console.warn("[RightPanel] No step selected for refresh");return}console.log("[RightPanel] Refreshing current step:",l.value.stepId);try{const F=await Hc(l.value.stepId);F&&l.value&&(l.value.agentExecution=F,l.value.completed=F.status==="FINISHED",l.value.current=F.status==="RUNNING",console.log("[RightPanel] Step refreshed successfully"),I())}catch(F){console.error("[RightPanel] Error refreshing step:",F)}};ce(()=>s.selectedStepId,async k=>{k?await y(k):l.value=null},{immediate:!0});const $=k=>{a.value=k??void 0},C=()=>{if(!a.value)return;const{scrollTop:k,scrollHeight:F,clientHeight:W}=a.value,Q=F-k-W<50,E=F>W;d.value=Q,u.value=E&&!Q,Q?b.value=!0:F-k-W>100&&(b.value=!1),console.log("[RightPanel] Scroll state check:",{scrollTop:k,scrollHeight:F,clientHeight:W,isAtBottom:Q,hasScrollableContent:E,showButton:u.value,shouldAutoScroll:b.value})},S=()=>{a.value&&(a.value.scrollTo({top:a.value.scrollHeight,behavior:"smooth"}),ke(()=>{b.value=!0,C()}))},I=()=>{!b.value||!a.value||ke(()=>{a.value&&(a.value.scrollTop=a.value.scrollHeight,console.log("[RightPanel] Auto scroll to bottom"))})},m=k=>{if(k===null||typeof k>"u"||k==="")return"N/A";try{const F=typeof k=="object"?k:JSON.parse(k);return JSON.stringify(F,null,2)}catch{return String(k)}},O=()=>{l.value=null,b.value=!0,a.value&&a.value.removeEventListener("scroll",C)},D=()=>{const k=()=>{const F=a.value;return F?($(F),F.addEventListener("scroll",C),b.value=!0,C(),console.log("[RightPanel] Scroll listener initialized successfully"),!0):(console.log("[RightPanel] Scroll container not found, retrying..."),!1)};ke(()=>{k()||setTimeout(()=>{k()},100)})};return ce(()=>s.currentRootPlanId,(k,F)=>{k&&k!==F?(h.value=k,g.value=!0,localStorage.setItem("jmanus-last-plan-id",k),localStorage.setItem("jmanus-has-executed-plan","true"),console.log("[RightPanel] New plan started:",k)):!k&&F&&console.log("[RightPanel] Plan execution finished, keeping last plan:",h.value)},{immediate:!0}),we(()=>{console.log("[RightPanel] Component mounted"),ke(()=>{D()})}),Te(()=>{console.log("[RightPanel] Component unmounting, cleaning up..."),O()}),e({handleStepSelected:y,refreshCurrentStep:v,updateDisplayedPlanProgress:k=>{console.log("[RightPanel] updateDisplayedPlanProgress called with rootPlanId:",k),k&&(h.value=k,g.value=!0,localStorage.setItem("jmanus-last-plan-id",k),localStorage.setItem("jmanus-has-executed-plan","true"),console.log("[RightPanel] Plan progress updated:",k))}}),(k,F)=>{var W,Q;return p(),f("div",Rd,[t("div",Ad,[t("div",xd,[t("div",{class:oe(["tab-item",{active:i.value==="details"}]),onClick:F[0]||(F[0]=E=>i.value="details")},[w(r(P),{icon:"carbon:events"}),t("span",null,c(r(n)("rightPanel.stepExecutionDetails")),1)],2),t("div",{class:oe(["tab-item",{active:i.value==="files"}]),onClick:F[1]||(F[1]=E=>i.value="files")},[w(r(P),{icon:"carbon:folder"}),t("span",null,c(r(n)("fileBrowser.title")),1)],2)])]),t("div",Dd,[i.value==="details"?(p(),f("div",Md,[l.value?(p(),f("div",Fd,[t("h3",null,c(l.value.title||l.value.description||r(n)("rightPanel.defaultStepTitle",{number:1})),1),l.value.agentExecution?(p(),f("div",Nd,[t("div",Ud,[t("span",Ld,c(r(n)("rightPanel.executingAgent"))+":",1),t("span",Bd,c(l.value.agentExecution.agentName),1)]),t("div",Od,[t("span",qd,c(r(n)("rightPanel.callingModel"))+":",1),t("span",jd,c(l.value.agentExecution.modelName),1)]),t("div",Vd,[t("span",Hd,c(r(n)("rightPanel.executionResult"))+":",1),t("span",{class:oe(["value",{success:l.value.agentExecution.status==="FINISHED"}])},c(l.value.agentExecution.status||r(n)("rightPanel.executing")),3)])])):L("",!0),t("div",Gd,[t("div",zd,[l.value.completed?(p(),le(r(P),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):l.value.current?(p(),le(r(P),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})):(p(),le(r(P),{key:2,icon:"carbon:time",class:"status-icon pending"})),t("span",Wd,c(R.value),1)])])])):L("",!0),t("div",{ref_key:"scrollContainer",ref:a,class:"step-details-scroll-container",onScroll:C},[l.value?(p(),f("div",Jd,[(W=l.value.agentExecution)!=null&&W.thinkActSteps&&l.value.agentExecution.thinkActSteps.length>0?(p(),f("div",Kd,[t("h4",null,c(r(n)("rightPanel.thinkAndActionSteps")),1),t("div",Xd,[(p(!0),f(ae,null,ie(l.value.agentExecution.thinkActSteps,(E,x)=>(p(),f("div",{key:x,class:"think-act-step"},[t("div",Yd,[t("span",Qd,"#"+c(x+1),1),t("span",{class:oe(["step-status",E.status])},c(E.status||r(n)("rightPanel.executing")),3)]),t("div",Zd,[t("h5",null,[w(r(P),{icon:"carbon:thinking"}),ne(" "+c(r(n)("rightPanel.thinking")),1)]),t("div",eu,[t("div",tu,[t("span",su,c(r(n)("rightPanel.input"))+":",1),t("pre",null,c(m(E.thinkInput)),1)]),t("div",nu,[t("span",ou,c(r(n)("rightPanel.output"))+":",1),t("pre",null,c(m(E.thinkOutput)),1)])])]),E.actionNeeded?(p(),f("div",au,[t("h5",null,[w(r(P),{icon:"carbon:play"}),ne(" "+c(r(n)("rightPanel.action")),1)]),t("div",lu,[(p(!0),f(ae,null,ie(E.actToolInfoList,(B,J)=>(p(),f("div",{key:J},[t("div",ru,[t("span",iu,c(r(n)("rightPanel.tool"))+":",1),t("span",cu,c(B.name||""),1)]),t("div",du,[t("span",uu,c(r(n)("rightPanel.toolParameters"))+":",1),t("pre",null,c(m(B.parameters)),1)]),t("div",pu,[t("span",hu,c(r(n)("rightPanel.executionResult"))+":",1),t("pre",null,c(m(B.result)),1)])]))),128))]),E.subPlanExecutionRecord?(p(),f("div",mu,[t("h5",null,[w(r(P),{icon:"carbon:tree-view"}),ne(" "+c(r(n)("rightPanel.subPlan")),1)]),t("div",fu,[t("div",vu,[t("div",gu,[t("span",bu,c(k.$t("rightPanel.subPlanId"))+":",1),t("span",yu,c(E.subPlanExecutionRecord.currentPlanId),1)]),E.subPlanExecutionRecord.title?(p(),f("div",_u,[t("span",$u,c(k.$t("rightPanel.title"))+":",1),t("span",wu,c(E.subPlanExecutionRecord.title),1)])):L("",!0),t("div",Su,[E.subPlanExecutionRecord.completed?(p(),le(r(P),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):(p(),le(r(P),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})),t("span",ku,c(E.subPlanExecutionRecord.completed?k.$t("rightPanel.status.completed"):k.$t("rightPanel.status.executing")),1)])])])])):L("",!0)])):L("",!0)]))),128))]),l.value.agentExecution&&!((Q=l.value.agentExecution.thinkActSteps)!=null&&Q.length)?(p(),f("div",Tu,[t("p",null,c(r(n)("rightPanel.noStepDetails")),1)])):l.value.agentExecution?L("",!0):(p(),f("div",Pu,[w(r(P),{icon:"carbon:information",class:"info-icon"}),t("h4",null,c(r(n)("rightPanel.stepInfo")),1),t("div",Eu,[t("div",Cu,[t("span",Iu,c(r(n)("rightPanel.stepName"))+":",1),t("span",Ru,c(l.value.title||l.value.description||l.value.stepId),1)]),l.value.description?(p(),f("div",Au,[t("span",xu,c(k.$t("rightPanel.description"))+":",1),t("span",Du,c(l.value.description),1)])):L("",!0),t("div",Mu,[t("span",Fu,c(k.$t("rightPanel.status.label"))+":",1),t("span",{class:oe(["value",{"status-completed":l.value.completed,"status-current":l.value.current,"status-pending":!l.value.completed&&!l.value.current}])},c(l.value.completed?k.$t("rightPanel.status.completed"):l.value.current?k.$t("rightPanel.status.executing"):k.$t("rightPanel.status.pending")),3)])]),t("p",Nu,c(r(n)("rightPanel.noExecutionInfo")),1)])),l.value.current&&!l.value.completed?(p(),f("div",Uu,[F[2]||(F[2]=t("div",{class:"execution-waves"},[t("div",{class:"wave wave-1"}),t("div",{class:"wave wave-2"}),t("div",{class:"wave wave-3"})],-1)),t("p",Lu,[w(r(P),{icon:"carbon:in-progress",class:"rotating-icon"}),ne(" "+c(r(n)("rightPanel.stepExecuting")),1)])])):L("",!0)])):(p(),f("div",Bu,[w(r(P),{icon:"carbon:events",class:"empty-icon"}),t("h3",null,c(r(n)("rightPanel.noStepSelected")),1),t("p",null,c(r(n)("rightPanel.selectStepHint")),1)]))])):L("",!0),w(qe,{name:"scroll-button"},{default:Me(()=>[u.value?(p(),f("button",{key:0,onClick:S,class:"scroll-to-bottom-btn",title:r(n)("rightPanel.scrollToBottom")},[w(r(P),{icon:"carbon:chevron-down"})],8,Ou)):L("",!0)]),_:1})],544)])):L("",!0),i.value==="files"?(p(),f("div",qu,[M.value?(p(),le(Id,{key:0,"plan-id":M.value},null,8,["plan-id"])):_.value?(p(),f("div",ju,[w(r(P),{icon:"carbon:folder-off"}),t("div",Vu,[t("h3",null,c(r(n)("fileBrowser.noFilesYet")),1),t("p",null,c(r(n)("fileBrowser.noPlanExecuting")),1),t("div",Hu,[w(r(P),{icon:"carbon:information"}),t("span",null,c(r(n)("fileBrowser.startTaskTip")),1)])])])):L("",!0)])):L("",!0)])])}}}),zu=pe(Gu,[["__scopeId","data-v-ea214380"]]);function Bt(){return{formatResponseText:g=>g?g.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/`([^`]+)`/g,"<code>$1</code>").replace(/\n/g,"<br>").replace(/```([\s\S]*?)```/g,"<pre><code>$1</code></pre>"):"",formatTimestamp:g=>{const d=new Date().getTime()-g.getTime();return d<6e4?"Just now":d<36e5?`${Math.floor(d/6e4)} minutes ago`:d<864e5?`${Math.floor(d/36e5)} hours ago`:g.toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},getMessageClasses:g=>de(()=>({user:g.type==="user",assistant:g.type==="assistant",streaming:g.isStreaming,"has-error":!!g.error,"has-thinking":!!g.thinking,"has-execution":!!g.planExecution})),formatFileSize:g=>{if(g===0)return"0 B";const u=1024,d=["B","KB","MB","GB"],b=Math.floor(Math.log(g)/Math.log(u));return parseFloat((g/Math.pow(u,b)).toFixed(2))+" "+d[b]},truncateText:(g,u=100)=>!g||g.length<=u?g:g.substring(0,u)+"...",stripHtml:g=>g.replace(/<[^>]*>/g,""),hasDisplayableContent:g=>!!(g.content||g.thinking||g.planExecution||g.error),getMessageStatus:g=>g.error?"Send failed":g.isStreaming?"Typing...":g.type==="assistant"&&!g.content&&!g.thinking?"Waiting for response":""}}const Wu=["data-message-id"],Ju={class:"user-content"},Ku={class:"message-text"},Xu={key:0,class:"attachments"},Yu={class:"attachment-name"},Qu={class:"attachment-size"},Zu={class:"message-timestamp"},ep={key:0,class:"message-status error"},tp={class:"status-text"},sp=ue({__name:"UserMessage",props:{message:{}},setup(o){const{formatTimestamp:e,formatFileSize:s}=Bt();return(n,a)=>{var l;return p(),f("div",{class:"user-message","data-message-id":n.message.id},[t("div",Ju,[t("div",Ku,c(n.message.content),1),(l=n.message.attachments)!=null&&l.length?(p(),f("div",Xu,[(p(!0),f(ae,null,ie(n.message.attachments,(i,h)=>(p(),f("div",{key:h,class:"attachment-item"},[w(r(P),{icon:"carbon:document",class:"attachment-icon"}),t("span",Yu,c(i.name),1),t("span",Qu,c(r(s)(i.size)),1)]))),128))])):L("",!0),t("div",Zu,c(r(e)(n.message.timestamp)),1)]),n.message.error?(p(),f("div",ep,[w(r(P),{icon:"carbon:warning",class:"status-icon"}),t("span",tp,c(n.message.error),1)])):L("",!0)],8,Wu)}}}),np=pe(sp,[["__scopeId","data-v-94372507"]]),op={class:"sub-plan-info"},ap={class:"sub-plan-details"},lp={class:"sub-plan-title"},rp={key:0,class:"nesting-level"},ip={class:"sub-plan-id"},cp={class:"sub-plan-meta"},dp={key:0,class:"trigger-tool"},up={class:"trigger-text"},pp={key:0,class:"sub-plan-progress"},hp={class:"progress-info"},mp={class:"progress-text"},fp={class:"progress-bar"},vp={key:1,class:"sub-plan-agents-steps"},gp={class:"agents-steps-header"},bp={class:"agents-label"},yp={class:"agents-steps-list"},_p=["onClick"],$p={class:"agent-step-header"},wp={class:"agent-name"},Sp={class:"sub-agent-execution-info"},kp={key:0,class:"agent-result"},Tp={class:"result-header"},Pp={class:"result-label"},Ep={class:"result-content"},Cp={key:1,class:"agent-error"},Ip={class:"error-header"},Rp={class:"error-label"},Ap={class:"error-content"},xp={key:2,class:"think-act-preview"},Dp={class:"think-act-header"},Mp={class:"think-act-label"},Fp={class:"think-act-steps-preview"},Np=["onClick"],Up={class:"step-number"},Lp={class:"step-description"},Bp={key:0,class:"more-steps"},Op={class:"more-steps-text"},qp={key:3,class:"nested-sub-plans"},jp={class:"nested-sub-plans-header"},Vp={class:"nested-label"},Hp={class:"nested-sub-plans-list"},Gp={key:4,class:"direct-sub-plans"},zp={class:"direct-sub-plans-header"},Wp={class:"direct-label"},Jp={class:"direct-sub-plans-list"},Kp=ue({__name:"RecursiveSubPlan",props:{subPlan:{},subPlanIndex:{},nestingLevel:{default:0},maxNestingDepth:{default:3},maxVisibleSteps:{default:2}},emits:["sub-plan-selected","step-selected"],setup(o,{emit:e}){const s=o,n=e,{t:a}=Pe(),l=()=>`nesting-level-${s.nestingLevel}`,i=()=>{var D,z;return s.subPlan.completed?"completed":((D=s.subPlan.agentExecutionSequence)==null?void 0:D.some(k=>k.status==="RUNNING"))?"running":((z=s.subPlan.agentExecutionSequence)==null?void 0:z.some(k=>k.status==="FINISHED"))?"in-progress":"pending"},h=()=>{switch(i()){case"completed":return a("chat.status.completed");case"running":return a("chat.status.executing");case"in-progress":return a("chat.status.inProgress");case"pending":return a("chat.status.pending");default:return a("chat.status.unknown")}},g=()=>{switch(i()){case"completed":return"carbon:checkmark";case"running":return"carbon:play";case"in-progress":return"carbon:in-progress";case"pending":default:return"carbon:dot-mark"}},u=()=>{var O;if(!((O=s.subPlan.agentExecutionSequence)!=null&&O.length))return 0;if(s.subPlan.completed)return 100;const m=d();return Math.min(100,m/s.subPlan.agentExecutionSequence.length*100)},d=()=>{var m;return(m=s.subPlan.agentExecutionSequence)!=null&&m.length?s.subPlan.agentExecutionSequence.filter(O=>O.status==="FINISHED").length:0},b=m=>{switch(m){case"FINISHED":return"completed";case"RUNNING":return"running";case"IDLE":default:return"pending"}},R=m=>{switch(m){case"FINISHED":return"carbon:checkmark";case"RUNNING":return"carbon:play";case"IDLE":default:return"carbon:dot-mark"}},M=m=>{switch(m){case"RUNNING":return a("chat.status.executing");case"FINISHED":return a("chat.status.completed");case"IDLE":default:return a("chat.status.pending")}},_=m=>{var O;return((O=m.thinkActSteps)==null?void 0:O.some(D=>D.subPlanExecutionRecord))??!1},y=m=>{var O;return((O=m.thinkActSteps)==null?void 0:O.filter(D=>D.subPlanExecutionRecord))??[]},v=()=>{n("sub-plan-selected",-1,s.subPlanIndex,s.subPlan)},$=(m,O)=>{const D=O.stepId||`subplan-${s.subPlanIndex}-agent-${m}`;n("step-selected",D)},C=(m,O,D)=>{const z=D.stepId||`subplan-${s.subPlanIndex}-agent-${m}`;n("step-selected",z)},S=(m,O,D)=>{n("sub-plan-selected",m,O,D)},I=m=>{n("step-selected",m)};return(m,O)=>{var z,k;const D=as("RecursiveSubPlan",!0);return p(),f("div",{class:oe(["recursive-sub-plan",l()])},[t("div",{class:"sub-plan-header",onClick:v},[t("div",op,[w(r(P),{icon:g(),class:"sub-plan-status-icon"},null,8,["icon"]),t("div",ap,[t("div",lp,[ne(c(m.subPlan.title||m.$t("chat.subPlan"))+" #"+c(m.subPlanIndex+1)+" ",1),(m.nestingLevel??0)>0?(p(),f("span",rp,"(L"+c((m.nestingLevel??0)+1)+")",1)):L("",!0)]),t("div",ip,c(m.subPlan.currentPlanId),1)])]),t("div",cp,[t("div",{class:oe(["sub-plan-status-badge",i()])},c(h()),3),m.subPlan.parentActToolCall?(p(),f("div",dp,[w(r(P),{icon:"carbon:function",class:"trigger-icon"}),t("span",up,c(m.subPlan.parentActToolCall.name),1)])):L("",!0)])]),(z=m.subPlan.agentExecutionSequence)!=null&&z.length?(p(),f("div",pp,[t("div",hp,[t("span",mp,c(m.$t("chat.progress"))+": "+c(d())+" / "+c(m.subPlan.agentExecutionSequence.length),1),t("div",fp,[t("div",{class:"progress-fill",style:ze({width:u()+"%"})},null,4)])])])):L("",!0),(k=m.subPlan.agentExecutionSequence)!=null&&k.length?(p(),f("div",vp,[t("div",gp,[t("span",bp,c(m.$t("chat.agentExecutions"))+":",1)]),t("div",yp,[(p(!0),f(ae,null,ie(m.subPlan.agentExecutionSequence,(F,W)=>{var Q,E;return p(),f("div",{key:F.id||W,class:oe(["agent-step-item",b(F.status)]),onClick:x=>$(W,F)},[t("div",$p,[w(r(P),{icon:R(F.status),class:"agent-icon"},null,8,["icon"]),t("span",wp,c(F.agentName||m.$t("chat.unknownAgent")),1),t("div",{class:oe(["agent-status-badge",b(F.status)])},c(M(F.status)),3)]),t("div",Sp,[F.result?(p(),f("div",kp,[t("div",Tp,[w(r(P),{icon:"carbon:checkmark",class:"result-icon"}),t("span",Pp,c(m.$t("chat.agentResult"))+":",1)]),t("pre",Ep,c(F.result),1)])):L("",!0),F.errorMessage?(p(),f("div",Cp,[t("div",Ip,[w(r(P),{icon:"carbon:warning",class:"error-icon"}),t("span",Rp,c(m.$t("chat.errorMessage"))+":",1)]),t("pre",Ap,c(F.errorMessage),1)])):L("",!0),(Q=F.thinkActSteps)!=null&&Q.length?(p(),f("div",xp,[t("div",Dp,[w(r(P),{icon:"carbon:thinking",class:"think-act-icon"}),t("span",Mp,c(m.$t("chat.thinkActSteps"))+" ("+c(F.thinkActSteps.length)+")",1)]),t("div",Fp,[(p(!0),f(ae,null,ie(F.thinkActSteps.slice(0,m.maxVisibleSteps??2),(x,B)=>(p(),f("div",{key:x.id||B,class:"think-act-step-preview",onClick:me(J=>C(W,B,F),["stop"])},[t("span",Up,"#"+c(B+1),1),t("span",Lp,c(x.actionDescription||m.$t("chat.thinking")),1),w(r(P),{icon:"carbon:arrow-right",class:"step-arrow"})],8,Np))),128)),F.thinkActSteps.length>(m.maxVisibleSteps??2)?(p(),f("div",Bp,[t("span",Op,c(m.$t("chat.andMoreSteps",{count:F.thinkActSteps.length-(m.maxVisibleSteps??2)})),1)])):L("",!0)])])):L("",!0),_(F)?(p(),f("div",qp,[t("div",jp,[w(r(P),{icon:"carbon:tree-view-alt",class:"nested-icon"}),t("span",Vp,c(m.$t("chat.nestedSubPlans")),1)]),t("div",Hp,[(p(!0),f(ae,null,ie(y(F),(x,B)=>(p(),le(D,{key:x.id||B,"sub-plan":x.subPlanExecutionRecord,"sub-plan-index":B,"nesting-level":(m.nestingLevel??0)+1,"max-nesting-depth":m.maxNestingDepth??3,"max-visible-steps":m.maxVisibleSteps??2,onSubPlanSelected:S,onStepSelected:I},null,8,["sub-plan","sub-plan-index","nesting-level","max-nesting-depth","max-visible-steps"]))),128))])])):L("",!0),(E=F.subPlanExecutionRecords)!=null&&E.length?(p(),f("div",Gp,[t("div",zp,[w(r(P),{icon:"carbon:tree-view",class:"direct-icon"}),t("span",Wp,c(m.$t("chat.directSubPlans")),1)]),t("div",Jp,[(p(!0),f(ae,null,ie(F.subPlanExecutionRecords,(x,B)=>(p(),le(D,{key:x.currentPlanId||B,"sub-plan":x,"sub-plan-index":B,"nesting-level":(m.nestingLevel??0)+1,"max-nesting-depth":m.maxNestingDepth??3,"max-visible-steps":m.maxVisibleSteps??2,onSubPlanSelected:S,onStepSelected:I},null,8,["sub-plan","sub-plan-index","nesting-level","max-nesting-depth","max-visible-steps"]))),128))])])):L("",!0)])],10,_p)}),128))])])):L("",!0)],2)}}}),Xp=pe(Kp,[["__scopeId","data-v-5ef3cbb7"]]),Yp={class:"execution-details"},Qp={key:0,class:"plan-overview"},Zp={class:"plan-header"},eh={class:"plan-title"},th={key:0,class:"parent-tool-call"},sh={class:"parent-tool-header"},nh={class:"tool-label"},oh={class:"tool-name"},ah={key:0,class:"tool-parameters"},lh={class:"param-label"},rh={class:"param-content"},ih={key:1,class:"agent-execution-container"},ch={class:"section-title"},dh=["onClick"],uh={class:"agent-info"},ph={class:"agent-details"},hh={class:"agent-name"},mh={class:"request-content"},fh={class:"agent-controls"},vh={class:"agent-execution-info"},gh={key:0,class:"agent-result"},bh={class:"result-header"},yh={class:"result-label"},_h={class:"result-content"},$h={key:1,class:"agent-error"},wh={class:"error-header"},Sh={class:"error-label"},kh={class:"error-content"},Th={key:0,class:"sub-plans-container"},Ph={class:"sub-plans-header"},Eh={class:"sub-plans-title"},Ch={class:"sub-plans-list"},Ih=ue({__name:"ExecutionDetails",props:{planExecution:{},genericInput:{}},emits:["agent-selected","sub-plan-selected","user-input-submitted","step-selected"],setup(o,{emit:e}){const s=o,n=e,{t:a}=Pe(),l=de(()=>{var y;return((y=s.planExecution)==null?void 0:y.agentExecutionSequence)??[]}),i=y=>{y.stepId?n("step-selected",y.stepId):console.warn("[ExecutionDetails] Agent execution has no stepId:",y)},h=()=>s.planExecution?s.planExecution.completed?"completed":l.value.some($=>$.status==="RUNNING")?"running":l.value.some($=>$.status==="FINISHED")?"in-progress":"pending":"unknown",g=()=>{switch(h()){case"completed":return a("chat.status.completed");case"running":return a("chat.status.executing");case"in-progress":return a("chat.status.inProgress");case"pending":return a("chat.status.pending");default:return a("chat.status.unknown")}},u=y=>{switch(y){case"RUNNING":return"running";case"FINISHED":return"completed";case"IDLE":default:return"pending"}},d=y=>{switch(y){case"RUNNING":return a("chat.status.executing");case"FINISHED":return a("chat.status.completed");case"IDLE":default:return a("chat.status.pending")}},b=y=>{switch(y){case"RUNNING":return"carbon:play";case"FINISHED":return"carbon:checkmark";case"IDLE":default:return"carbon:dot-mark"}},R=(y,v,$)=>{n("sub-plan-selected",y,v,$)},M=y=>{n("step-selected",y)},_=y=>{if(!y)return"";try{const v=JSON.parse(y);return JSON.stringify(v,null,2)}catch{return y}};return(y,v)=>{var $,C,S;return p(),f("div",Yp,[y.planExecution?(p(),f("div",Qp,[t("div",Zp,[t("h3",eh,c(y.planExecution.title||y.$t("chat.planExecution")),1),t("div",{class:oe(["plan-status-badge",h()])},c(g()),3)]),y.planExecution.parentActToolCall?(p(),f("div",th,[t("div",sh,[w(r(P),{icon:"carbon:flow",class:"tool-icon"}),t("span",nh,c(y.$t("chat.triggeredByTool"))+":",1),t("span",oh,c(y.planExecution.parentActToolCall.name),1)]),y.planExecution.parentActToolCall.parameters?(p(),f("div",ah,[t("span",lh,c(y.$t("common.parameters"))+":",1),t("pre",rh,c(_(y.planExecution.parentActToolCall.parameters)),1)])):L("",!0)])):L("",!0)])):L("",!0),(((C=($=y.planExecution)==null?void 0:$.agentExecutionSequence)==null?void 0:C.length)??0)>0?(p(),f("div",ih,[t("h4",ch,c(y.$t("chat.agentExecutionSequence")),1),(p(!0),f(ae,null,ie((S=y.planExecution)==null?void 0:S.agentExecutionSequence,(I,m)=>{var O;return p(),f("div",{key:I.id||m,class:oe(["agent-execution-item",u(I.status)])},[t("div",{class:"agent-header",onClick:D=>i(I)},[t("div",uh,[w(r(P),{icon:b(I.status),class:"agent-status-icon"},null,8,["icon"]),t("div",ph,[t("div",hh,c(I.agentName||y.$t("chat.unknownAgent")),1),t("pre",mh,c(I.agentRequest),1)])]),t("div",fh,[t("div",{class:oe(["agent-status-badge",u(I.status)])},c(d(I.status)),3),w(r(P),{icon:"carbon:arrow-right",class:"step-select-icon"})])],8,dh),t("div",vh,[I.result?(p(),f("div",gh,[t("div",bh,[w(r(P),{icon:"carbon:checkmark",class:"result-icon"}),t("span",yh,c(y.$t("chat.agentResult"))+":",1)]),t("pre",_h,c(I.result),1)])):L("",!0),I.errorMessage?(p(),f("div",$h,[t("div",wh,[w(r(P),{icon:"carbon:warning",class:"error-icon"}),t("span",Sh,c(y.$t("chat.errorMessage"))+":",1)]),t("pre",kh,c(I.errorMessage),1)])):L("",!0)]),(O=I.subPlanExecutionRecords)!=null&&O.length?(p(),f("div",Th,[t("div",Ph,[w(r(P),{icon:"carbon:tree-view",class:"sub-plans-icon"}),t("span",Eh,c(y.$t("chat.subPlanExecutions"))+" ("+c(I.subPlanExecutionRecords.length)+") ",1)]),t("div",Ch,[(p(!0),f(ae,null,ie(I.subPlanExecutionRecords,(D,z)=>(p(),le(Xp,{key:D.currentPlanId||z,"sub-plan":D,"sub-plan-index":z,"nesting-level":0,"max-nesting-depth":3,"max-visible-steps":2,onSubPlanSelected:R,onStepSelected:M},null,8,["sub-plan","sub-plan-index"]))),128))])])):L("",!0)],2)}),128))])):L("",!0)])}}}),Us=pe(Ih,[["__scopeId","data-v-2d936ebb"]]),Rh={key:0,class:"thinking-section"},Ah={class:"thinking-header"},xh={class:"thinking-avatar"},Dh={class:"thinking-label"},Mh={class:"thinking-content"},Fh={key:0,class:"thinking"},Nh={key:2,class:"default-processing"},Uh={class:"processing-indicator"},Lh=ue({__name:"ThinkingSection",props:{thinking:{},thinkingDetails:{},planExecution:{},stepActions:{},genericInput:{},hasContent:{type:Boolean}},emits:["agent-selected","sub-plan-selected","user-input-submitted","step-selected"],setup(o,{emit:e}){const s=o,n=e,a=de(()=>{var u,d,b,R;return s.thinking||(((d=(u=s.thinkingDetails)==null?void 0:u.agentExecutionSequence)==null?void 0:d.length)??0)>0||(((R=(b=s.planExecution)==null?void 0:b.agentExecutionSequence)==null?void 0:R.length)??0)>0}),l=(u,d)=>{n("agent-selected",u,d)},i=(u,d,b)=>{n("sub-plan-selected",u,d,b)},h=u=>{n("user-input-submitted",u)},g=u=>{n("step-selected",u)};return(u,d)=>a.value?(p(),f("div",Rh,[t("div",Ah,[t("div",xh,[w(r(P),{icon:"carbon:thinking",class:"thinking-icon"})]),t("div",Dh,c(u.$t("chat.thinkingLabel")),1)]),t("div",Mh,[u.thinking?(p(),f("div",Fh,[w(r(P),{icon:"carbon:thinking",class:"thinking-icon"}),t("span",null,c(u.thinking),1)])):L("",!0),u.thinkingDetails||u.planExecution?(p(),le(Us,{key:1,"plan-execution":u.thinkingDetails||u.planExecution,"step-actions":u.stepActions||[],"generic-input":u.genericInput||"",onAgentSelected:l,onSubPlanSelected:i,onUserInputSubmitted:h,onStepSelected:g},null,8,["plan-execution","step-actions","generic-input"])):!u.hasContent&&u.thinking?(p(),f("div",Nh,[t("div",Uh,[d[0]||(d[0]=t("div",{class:"thinking-dots"},[t("span"),t("span"),t("span")],-1)),t("span",null,c(u.thinking||u.$t("chat.thinkingProcessing")),1)])])):L("",!0)])])):L("",!0)}}),Bh=pe(Lh,[["__scopeId","data-v-c341487f"]]);class Ot{static async getDetails(e){try{const s=await fetch(`${this.BASE_URL}/details/${e}`);if(s.status===404)return null;if(!s.ok){const l=await s.text();throw new Error(`Failed to get detailed information: ${s.status} - ${l}`)}const n=await s.text(),a=JSON.parse(n);return a&&typeof a=="object"&&!a.currentPlanId&&(a.currentPlanId=e),a}catch(s){return console.error("[CommonApiService] Failed to get plan details:",s),null}}static async submitFormInput(e,s){const n=await fetch(`${this.BASE_URL}/submit-input/${e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!n.ok){let l;try{l=await n.json()}catch{l={message:`Failed to submit form input: ${n.status}`}}throw new Error(l.message||`Failed to submit form input: ${n.status}`)}const a=n.headers.get("content-type");return a&&a.indexOf("application/json")!==-1?await n.json():{success:!0}}static async getAllPrompts(){try{const e=await fetch(this.BASE_URL);return await(await this.handleResponse(e)).json()}catch(e){throw console.error("Failed to get Prompt list:",e),e}}static async handleResponse(e){if(!e.ok)try{const s=await e.json();throw new Error(s.message||`API request failed: ${e.status}`)}catch{throw new Error(`API request failed: ${e.status} ${e.statusText}`)}return e}}ge(Ot,"BASE_URL","/api/executor");const Oh={class:"user-input-form-container"},qh={class:"user-input-header"},jh={class:"user-input-title"},Vh={class:"user-input-message"},Hh={key:0,class:"form-description"},Gh={key:0,class:"form-grid"},zh=["for"],Wh=["id","name","placeholder","required","onUpdate:modelValue"],Jh=["id","name","placeholder","required","onUpdate:modelValue"],Kh=["id","name","placeholder","required","onUpdate:modelValue"],Xh=["id","name","placeholder","required","onUpdate:modelValue"],Yh=["id","name","placeholder","required","onUpdate:modelValue"],Qh=["id","name","required","onUpdate:modelValue"],Zh={value:""},em=["value"],tm={key:6,class:"checkbox-group"},sm=["id","name","value","onUpdate:modelValue"],nm={class:"checkbox-label"},om={key:7,class:"radio-group"},am=["id","name","value","onUpdate:modelValue"],lm={class:"radio-label"},rm=["id","name","placeholder","required","onUpdate:modelValue"],im={key:1,class:"form-group"},cm={for:"form-input-genericInput"},dm={type:"submit",class:"submit-user-input-btn"},um=ue({__name:"UserInputForm",props:{userInputWaitState:{},planId:{},genericInput:{}},emits:["user-input-submitted"],setup(o,{emit:e}){const s=o,n=e;Pe();const a=st({}),l=N(s.genericInput||""),i=N(!1),h=N(null),g=()=>{var M;const R=(M=s.userInputWaitState)==null?void 0:M.formInputs;R&&R.length>0&&(!i.value||Object.keys(a).length!==R.length)&&(R.forEach((y,v)=>{(!(v in a)||y.type==="checkbox"&&!Array.isArray(a[v])||y.type!=="checkbox"&&a[v]==="")&&(y.type==="checkbox"?a[v]=[]:a[v]="")}),i.value=!0)};we(()=>{g()}),ce(()=>{var R;return(R=s.userInputWaitState)==null?void 0:R.formInputs},()=>{h.value&&clearTimeout(h.value),h.value=setTimeout(()=>{i.value=!1,g()},100)},{deep:!0});const u=async()=>{var R;try{const M={},_=(R=s.userInputWaitState)==null?void 0:R.formInputs;_&&_.length>0?Object.entries(a).forEach(([y,v])=>{const $=parseInt(y,10),C=_[$];if(C){const S=C.label||`input_${y}`;Array.isArray(v)?M[S]=v.join(", "):M[S]=v}}):M.genericInput=l.value,console.log("[UserInputForm] Submitting user input:",M,"for planId:",s.planId),s.planId?(await Ot.submitFormInput(s.planId,M),console.log("[UserInputForm] User input submitted successfully")):console.error("[UserInputForm] No planId available for user input submission"),n("user-input-submitted",M)}catch(M){console.error("[UserInputForm] User input submission failed:",M)}},d=R=>R?Array.isArray(R)?R:typeof R=="string"?R.split(",").map(M=>M.trim()).filter(M=>M.length>0):[]:[],b=R=>typeof R=="boolean"?R:typeof R=="string"?R==="true":!1;return Te(()=>{h.value&&clearTimeout(h.value)}),(R,M)=>{var _,y,v,$,C;return p(),f("div",Oh,[t("div",qh,[w(r(P),{icon:"carbon:user",class:"user-icon"}),t("h4",jh,c(R.$t("chat.userInputRequired")),1)]),t("p",Vh,c(((_=R.userInputWaitState)==null?void 0:_.title)??R.$t("chat.userInput.message")),1),(y=R.userInputWaitState)!=null&&y.formDescription?(p(),f("p",Hh,c((v=R.userInputWaitState)==null?void 0:v.formDescription),1)):L("",!0),t("form",{onSubmit:me(u,["prevent"]),class:"user-input-form"},[($=R.userInputWaitState)!=null&&$.formInputs&&R.userInputWaitState.formInputs.length>0?(p(),f("div",Gh,[(p(!0),f(ae,null,ie((C=R.userInputWaitState)==null?void 0:C.formInputs,(S,I)=>(p(),f("div",{key:I,class:"form-group"},[t("label",{for:`form-input-${S.label.replace(/\W+/g,"_")}`},c(S.label)+c(b(S.required)?" *":"")+": ",9,zh),!S.type||S.type==="text"?se((p(),f("input",{key:0,type:"text",id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,placeholder:S.placeholder||"",required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input"},null,8,Wh)),[[re,a[I]]]):S.type==="email"?se((p(),f("input",{key:1,type:"email",id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,placeholder:S.placeholder||"",required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input"},null,8,Jh)),[[re,a[I]]]):S.type==="number"?se((p(),f("input",{key:2,type:"number",id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,placeholder:S.placeholder||"",required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input"},null,8,Kh)),[[re,a[I]]]):S.type==="password"?se((p(),f("input",{key:3,type:"password",id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,placeholder:S.placeholder||"",required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input"},null,8,Xh)),[[re,a[I]]]):S.type==="textarea"?se((p(),f("textarea",{key:4,id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,placeholder:S.placeholder||"",required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input form-textarea",rows:"4"},null,8,Yh)),[[re,a[I]]]):S.type==="select"&&S.options?se((p(),f("select",{key:5,id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input form-select"},[t("option",Zh,c(R.$t("selectCommon.pleaseSelect")),1),(p(!0),f(ae,null,ie(d(S.options),m=>(p(),f("option",{key:m,value:m},c(m),9,em))),128))],8,Qh)),[[nt,a[I]]]):S.type==="checkbox"&&S.options?(p(),f("div",tm,[(p(!0),f(ae,null,ie(d(S.options),m=>(p(),f("label",{key:m,class:"checkbox-item"},[se(t("input",{type:"checkbox",id:`form-input-${S.label.replace(/\W+/g,"_")}-${m.replace(/\W+/g,"_")}`,name:S.label,value:m,"onUpdate:modelValue":O=>a[I]=O,class:"form-checkbox"},null,8,sm),[[it,a[I]]]),t("span",nm,c(m),1)]))),128))])):S.type==="radio"&&S.options?(p(),f("div",om,[(p(!0),f(ae,null,ie(d(S.options),m=>(p(),f("label",{key:m,class:"radio-item"},[se(t("input",{type:"radio",id:`form-input-${S.label.replace(/\W+/g,"_")}-${m.replace(/\W+/g,"_")}`,name:S.label,value:m,"onUpdate:modelValue":O=>a[I]=O,class:"form-radio"},null,8,am),[[Os,a[I]]]),t("span",lm,c(m),1)]))),128))])):se((p(),f("input",{key:8,type:"text",id:`form-input-${S.label.replace(/\W+/g,"_")}`,name:S.label,placeholder:S.placeholder||"",required:b(S.required),"onUpdate:modelValue":m=>a[I]=m,class:"form-input"},null,8,rm)),[[re,a[I]]])]))),128))])):(p(),f("div",im,[t("label",cm,c(R.$t("common.input"))+":",1),se(t("input",{type:"text",id:"form-input-genericInput",name:"genericInput","onUpdate:modelValue":M[0]||(M[0]=S=>l.value=S),class:"form-input"},null,512),[[re,l.value]])])),t("button",dm,c(R.$t("chat.userInput.submit")),1)],32)])}}}),pm=pe(um,[["__scopeId","data-v-043b591c"]]),hm={class:"response-section"},mm={class:"response-header"},fm={class:"response-avatar"},vm={class:"response-name"},gm={key:0,class:"response-timestamp"},bm={class:"response-content"},ym={key:1,class:"final-response"},_m=["innerHTML"],$m={class:"response-actions"},wm=["title"],Sm=["title"],km={key:2,class:"response-placeholder"},Tm={class:"typing-indicator"},Pm={class:"typing-text"},Em={key:3,class:"response-error"},Cm={class:"error-text"},Im={key:4,class:"response-empty"},Rm={class:"empty-text"},Am=ue({__name:"ResponseSection",props:{content:{},isStreaming:{type:Boolean},error:{},timestamp:{},userInputWaitState:{},planId:{},genericInput:{}},emits:["copy","regenerate","retry","user-input-submitted"],setup(o,{emit:e}){const s=o,n=e,{formatResponseText:a,formatTimestamp:l}=Bt(),i=async()=>{if(s.content)try{const d=s.content.replace(/<[^>]*>/g,"");await navigator.clipboard.writeText(d),n("copy")}catch(d){console.error("Failed to copy to clipboard:",d)}},h=()=>{n("regenerate")},g=()=>{n("retry")},u=d=>{console.log("[ResponseSection] User input submitted:",d),n("user-input-submitted",d)};return(d,b)=>{var R;return p(),f("div",hm,[t("div",mm,[t("div",fm,[w(r(P),{icon:"carbon:bot",class:"bot-icon"})]),t("div",vm,c(d.$t("chat.botName")),1),d.timestamp?(p(),f("div",gm,c(r(l)(d.timestamp)),1)):L("",!0)]),t("div",bm,[(R=d.userInputWaitState)!=null&&R.waiting?(p(),le(pm,ls({key:0,"user-input-wait-state":d.userInputWaitState},d.planId?{"plan-id":d.planId}:{},{"generic-input":d.genericInput??"",onUserInputSubmitted:u}),null,16,["user-input-wait-state","generic-input"])):L("",!0),d.content?(p(),f("div",ym,[t("div",{class:"response-text",innerHTML:r(a)(d.content)},null,8,_m),t("div",$m,[t("button",{class:"action-btn copy-btn",onClick:i,title:d.$t("chat.copyResponse")},[w(r(P),{icon:"carbon:copy"})],8,wm),t("button",{class:"action-btn regenerate-btn",onClick:h,title:d.$t("chat.regenerateResponse")},[w(r(P),{icon:"carbon:renew"})],8,Sm)])])):d.isStreaming?(p(),f("div",km,[t("div",Tm,[b[0]||(b[0]=t("div",{class:"typing-dots"},[t("span"),t("span"),t("span")],-1)),t("span",Pm,c(d.$t("chat.thinkingResponse")),1)])])):d.error?(p(),f("div",Em,[w(r(P),{icon:"carbon:warning",class:"error-icon"}),t("span",Cm,c(d.error),1),t("button",{class:"retry-btn",onClick:g},c(d.$t("chat.retry")),1)])):(p(),f("div",Im,[t("span",Rm,c(d.$t("chat.waitingForResponse")),1)]))])])}}}),xm=pe(Am,[["__scopeId","data-v-6581a34f"]]),Dm={class:"assistant-message"},Mm=ue({__name:"AssistantMessage",props:{message:{},isStreaming:{type:Boolean}},emits:["copy","regenerate","retry","step-selected"],setup(o,{emit:e}){const s=o,n=e,a=()=>{n("copy",s.message.id)},l=()=>{n("regenerate",s.message.id)},i=()=>{n("retry",s.message.id)},h=(u,d)=>{console.log("[AssistantMessage] User input submitted:",d,"for message:",u.id)},g=u=>{console.log("[AssistantMessage] Step selected:",u),n("step-selected",u)};return(u,d)=>{var b,R,M,_;return p(),f("div",Dm,[u.message.thinkingDetails?(p(),le(Bh,{key:0,"thinking-details":u.message.thinkingDetails,onStepSelected:g},null,8,["thinking-details"])):L("",!0),u.message.planExecution?(p(),le(Us,{key:1,"plan-execution":u.message.planExecution,"step-actions":u.message.stepActions||[],"generic-input":u.message.genericInput||"",onStepSelected:g},null,8,["plan-execution","step-actions","generic-input"])):L("",!0),u.message.content||u.message.error||u.isStreaming||(R=(b=u.message.planExecution)==null?void 0:b.userInputWaitState)!=null&&R.waiting?(p(),le(xm,ls({key:2,content:u.message.content||"","is-streaming":u.isStreaming||!1},{...u.message.error?{error:u.message.error}:{},...(M=u.message.planExecution)!=null&&M.userInputWaitState?{userInputWaitState:u.message.planExecution.userInputWaitState}:{},...(_=u.message.planExecution)!=null&&_.currentPlanId?{planId:u.message.planExecution.currentPlanId}:{}},{timestamp:u.message.timestamp,"generic-input":u.message.genericInput||"",onCopy:a,onRegenerate:l,onRetry:i,onUserInputSubmitted:d[0]||(d[0]=y=>h(u.message,y))}),null,16,["content","is-streaming","timestamp","generic-input"])):L("",!0)])}}}),Fm=pe(Mm,[["__scopeId","data-v-3810c279"]]),Nm=ue({__name:"ChatMessage",props:{message:{},isStreaming:{type:Boolean}},emits:["copy","regenerate","retry","step-selected"],setup(o,{emit:e}){const s=o,n=e,{getMessageClasses:a}=Bt(),l=de(()=>a(s.message)),i=d=>{n("copy",d)},h=d=>{n("regenerate",d)},g=d=>{n("retry",d)},u=d=>{n("step-selected",d)};return(d,b)=>(p(),f("div",{class:oe(["chat-message",l.value])},[d.message.type==="user"?(p(),le(np,{key:0,message:d.message},null,8,["message"])):d.message.type==="assistant"?(p(),le(Fm,{key:1,message:d.message,"is-streaming":d.isStreaming||!1,onCopy:i,onRegenerate:h,onRetry:g,onStepSelected:u},null,8,["message","is-streaming"])):L("",!0)],2))}}),Um=pe(Nm,[["__scopeId","data-v-218b3735"]]);function Dt(o){const e={...o};return"agentExecutionSequence"in o&&Array.isArray(o.agentExecutionSequence)&&(e.agentExecutionSequence=o.agentExecutionSequence.map(s=>Lm(s))),e}function Lm(o){const e={...o};return"subPlanExecutionRecords"in o&&Array.isArray(o.subPlanExecutionRecords)&&(e.subPlanExecutionRecords=o.subPlanExecutionRecords.map(s=>Dt(s))),e}function Bm(o){const e={...o};return"thinkingDetails"in o&&o.thinkingDetails&&(e.thinkingDetails=Dt(o.thinkingDetails)),"planExecution"in o&&o.planExecution&&(e.planExecution=Dt(o.planExecution)),"attachments"in o&&Array.isArray(o.attachments)&&(e.attachments=[...o.attachments]),e}function Om(){const o=N([]),e=N(!1),s=N(null),n=N(null),a=de(()=>o.value.length>0?o.value[o.value.length-1]:null),l=de(()=>s.value!==null),i=de(()=>o.value.length>0),h=(I,m,O)=>{const D={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,type:I,content:m,timestamp:new Date,isStreaming:!1,...O};return o.value.push(D),D},g=(I,m)=>{const O=o.value.findIndex(D=>D.id===I);O!==-1&&(o.value[O]={...o.value[O],...m})},u=I=>{const m=o.value.findIndex(O=>O.id===I);m!==-1&&o.value.splice(m,1)},d=()=>{o.value=[],s.value=null,n.value=null},b=I=>{s.value=I,g(I,{isStreaming:!0})},R=I=>{I&&g(I,{isStreaming:!1}),s.value===I&&(s.value=null)},M=I=>{n.value=I},_=(I,m,O="content")=>{const D=o.value.find(z=>z.id===I);D&&typeof D[O]=="string"&&g(I,{[O]:D[O]+m})},y=(I,m)=>{g(I,{thinkingDetails:m})},v=(I,m)=>{g(I,{thinking:m})},$=(I,m)=>{g(I,{planExecution:m})},C=I=>o.value.find(m=>m.id===I),S=I=>o.value.findIndex(m=>m.id===I);return{messages:Ze(o),isLoading:e,streamingMessageId:Ze(s),activeMessageId:Ze(n),lastMessage:a,isStreaming:l,hasMessages:i,addMessage:h,updateMessage:g,removeMessage:u,clearMessages:d,startStreaming:b,stopStreaming:R,setActiveMessage:M,appendToMessage:_,updateMessageThinking:v,updateMessageThinkingDetails:y,updateMessagePlanExecution:$,findMessage:C,getMessageIndex:S}}function qm(o){const e=N(!1),s=N(!0),n=150,a=async(v=!0)=>{o.value&&(await ke(),o.value.scrollTo({top:o.value.scrollHeight,behavior:v?"smooth":"auto"}))},l=()=>{if(!o.value)return!1;const{scrollTop:v,scrollHeight:$,clientHeight:C}=o.value;return $-v-C<n},i=()=>{if(!o.value)return;const v=l();e.value=!v&&o.value.scrollHeight>o.value.clientHeight,v&&(s.value=!0)},h=()=>{i(),l()||(s.value=!1)},g=async()=>{s.value&&await a()},u=async()=>{s.value=!0,await a()},d=(v,$=!0)=>{if(!o.value)return;const C=o.value.querySelector(`[data-message-id="${v}"]`);C&&C.scrollIntoView({behavior:$?"smooth":"auto",block:"center"})},b=()=>{if(!o.value)return{scrollTop:0,scrollHeight:0,clientHeight:0,isAtTop:!0,isAtBottom:!0,scrollPercentage:0};const{scrollTop:v,scrollHeight:$,clientHeight:C}=o.value,S=v===0,I=v+C>=$-5,m=$>C?v/($-C)*100:100;return{scrollTop:v,scrollHeight:$,clientHeight:C,isAtTop:S,isAtBottom:I,scrollPercentage:m}},R=()=>{o.value&&o.value.addEventListener("scroll",h,{passive:!0})},M=()=>{o.value&&o.value.removeEventListener("scroll",h)},_=()=>{R(),i()},y=()=>{M()};return we(()=>{ke(()=>{_()})}),Te(()=>{y()}),{showScrollToBottom:Ze(e),isAutoScrollEnabled:Ze(s),scrollToBottom:a,autoScrollToBottom:g,forceScrollToBottom:u,scrollToMessage:d,isNearBottom:l,checkScrollPosition:i,getScrollInfo:b,addScrollListener:R,removeScrollListener:M,initializeScrollBehavior:_,cleanupScrollBehavior:y}}class qt{static async sendMessage(e){return Ws.withLlmCheck(async()=>{const s={...e,isVueRequest:!0},n=await fetch(`${this.BASE_URL}/execute`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!n.ok)throw new Error(`API request failed: ${n.status}`);return await n.json()})}}ge(qt,"BASE_URL","/api/executor");const jm=Object.freeze(Object.defineProperty({__proto__:null,DirectApiService:qt},Symbol.toStringTag,{value:"Module"})),Be=class Be{constructor(){ge(this,"POLL_INTERVAL",5e3);ge(this,"state",st({activePlanId:null,lastSequenceSize:0,isPolling:!1,pollTimer:null}));ge(this,"callbacks",{});ge(this,"planExecutionCache",new Map);ge(this,"uiStateCache",new Map);console.log("[PlanExecutionManager] Initialized with callback-based event system")}getCachedPlanRecord(e){return this.planExecutionCache.get(e)}getCachedUIState(e){return this.uiStateCache.get(e)}setCachedUIState(e,s){this.uiStateCache.set(e,s),console.log(`[PlanExecutionManager] Cached UI state for rootPlanId: ${e}`)}getAllCachedRecords(){return new Map(this.planExecutionCache)}hasCachedPlanRecord(e){return this.planExecutionCache.has(e)}setCachedPlanRecord(e,s){this.planExecutionCache.set(e,s),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${e}`)}clearCachedPlanRecord(e){const s=this.planExecutionCache.delete(e);return s&&console.log(`[PlanExecutionManager] Cleared cached plan execution record for rootPlanId: ${e}`),s}clearAllCachedRecords(){const e=this.planExecutionCache.size,s=this.uiStateCache.size;this.planExecutionCache.clear(),this.uiStateCache.clear(),console.log(`[PlanExecutionManager] Cleared all caches - Plans: ${e}, UI States: ${s}`)}static getInstance(){return Be.instance||(Be.instance=new Be),Be.instance}getActivePlanId(){return this.state.activePlanId}getState(){return this.state}setEventCallbacks(e){this.callbacks={...this.callbacks,...e},console.log("[PlanExecutionManager] Event callbacks set:",Object.keys(e))}async handleUserMessageSendRequested(e){if(this.validateAndPrepareUIForNewRequest(e))try{if(await this.sendUserMessageAndSetPlanId(e),this.state.activePlanId)this.initiatePlanExecutionSequence(e,this.state.activePlanId);else throw new Error("Failed to get valid plan ID")}catch(s){console.error("[PlanExecutionManager] Failed to send user message:",s);const n=this.state.activePlanId??"error";this.setCachedUIState(n,{enabled:!0}),this.emitChatInputUpdateState(n),this.state.activePlanId=null}}handlePlanExecutionRequested(e,s){console.log("[PlanExecutionManager] Received plan execution request:",{planId:e,query:s}),e?(this.state.activePlanId=e,this.initiatePlanExecutionSequence(s??"Execute Plan",e)):console.error("[PlanExecutionManager] Invalid plan execution request: missing planId")}handleCachedPlanExecution(e,s){const n=this.getCachedPlanRecord(e);return n!=null&&n.currentPlanId?(console.log(`[PlanExecutionManager] Found cached plan execution record for rootPlanId: ${e}`),this.handlePlanExecutionRequested(n.currentPlanId,s),!0):(console.log(`[PlanExecutionManager] No cached plan execution record found for rootPlanId: ${e}`),!1)}validateAndPrepareUIForNewRequest(e){if(!e)return console.warn("[PlanExecutionManager] Query is empty"),!1;if(this.state.activePlanId)return!1;this.emitChatInputClear();const s=this.state.activePlanId??"ui-state";return this.setCachedUIState(s,{enabled:!1,placeholder:"Processing..."}),this.emitChatInputUpdateState(s),!0}async sendUserMessageAndSetPlanId(e){try{const s=await qt.sendMessage({input:e});if(s!=null&&s.planId)return this.state.activePlanId=s.planId,s;if(s!=null&&s.planTemplateId)return this.state.activePlanId=s.planTemplateId,{...s,planId:s.planTemplateId};throw console.error("[PlanExecutionManager] Failed to get planId from response:",s),new Error("Failed to get valid planId from API response")}catch(s){throw console.error("[PlanExecutionManager] API call failed:",s),s}}initiatePlanExecutionSequence(e,s){console.log(`[PlanExecutionManager] Starting plan execution sequence for query: "${e}", planId: ${s}`);const n=s;this.emitDialogRoundStart(n),this.startPolling()}handlePlanCompletion(e){this.emitPlanCompleted(e.rootPlanId??""),this.state.lastSequenceSize=0,this.stopPolling();try{setTimeout(async()=>{if(this.state.activePlanId)try{await et.deletePlanTemplate(this.state.activePlanId),console.log(`[PlanExecutionManager] Plan template ${this.state.activePlanId} deleted successfully`)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}},5e3)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}e.completed&&(this.state.activePlanId=null,this.emitChatInputUpdateState(e.rootPlanId??""))}handlePlanError(e){this.emitPlanError(e.message??""),this.state.lastSequenceSize=0,this.stopPolling();try{setTimeout(async()=>{if(this.state.activePlanId)try{await et.deletePlanTemplate(this.state.activePlanId),console.log(`[PlanExecutionManager] Plan template ${this.state.activePlanId} deleted successfully`)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}},5e3)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}}async pollPlanStatus(){if(this.state.activePlanId){if(this.state.isPolling){console.log("[PlanExecutionManager] Previous polling still in progress, skipping");return}try{this.state.isPolling=!0;const e=await this.getPlanDetails(this.state.activePlanId);if(!e){console.warn("[PlanExecutionManager] No details received from API - this might be a temporary network issue");return}if(e.status&&e.status==="failed"&&e.message&&!e.message.includes("Failed to get detailed information")){this.handlePlanError(e);return}e.rootPlanId&&this.setCachedPlanRecord(e.rootPlanId,e),this.emitPlanUpdate(e.rootPlanId??""),e.completed&&this.handlePlanCompletion(e)}catch(e){console.error("[PlanExecutionManager] Failed to poll plan status:",e)}finally{this.state.isPolling=!1}}}async getPlanDetails(e){try{const s=await Ot.getDetails(e);return s!=null&&s.rootPlanId&&(this.planExecutionCache.set(s.rootPlanId,s),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${s.rootPlanId}`)),s}catch(s){return console.error("[PlanExecutionManager] Failed to get plan details:",s),{currentPlanId:e,status:"failed",message:s instanceof Error?s.message:"Failed to get plan"}}}startPolling(){this.state.pollTimer&&clearInterval(this.state.pollTimer),this.state.pollTimer=window.setInterval(()=>{this.pollPlanStatus()},this.POLL_INTERVAL),console.log("[PlanExecutionManager] Started polling")}async pollPlanStatusImmediately(){console.log("[PlanExecutionManager] Polling plan status immediately"),await this.pollPlanStatus()}stopPolling(){this.state.pollTimer&&(clearInterval(this.state.pollTimer),this.state.pollTimer=null),console.log("[PlanExecutionManager] Stopped polling")}cleanup(){this.stopPolling(),this.state.activePlanId=null,this.state.lastSequenceSize=0,this.state.isPolling=!1,this.clearAllCachedRecords()}emitChatInputClear(){this.callbacks.onChatInputClear&&this.callbacks.onChatInputClear()}emitChatInputUpdateState(e){this.callbacks.onChatInputUpdateState&&this.callbacks.onChatInputUpdateState(e)}emitDialogRoundStart(e){this.callbacks.onDialogRoundStart&&this.callbacks.onDialogRoundStart(e)}emitPlanUpdate(e){this.callbacks.onPlanUpdate&&this.callbacks.onPlanUpdate(e)}emitPlanCompleted(e){this.callbacks.onPlanCompleted&&this.callbacks.onPlanCompleted(e)}emitPlanError(e){this.callbacks.onPlanError&&this.callbacks.onPlanError(e)}};ge(Be,"instance",null);let Mt=Be;const Fe=Mt.getInstance(),Vm={class:"chat-container"},Hm={key:0,class:"loading-message"},Gm={class:"loading-content"},zm=["title"],Wm=ue({__name:"ChatContainer",props:{mode:{default:"plan"},initialPrompt:{default:""}},emits:["step-selected"],setup(o,{expose:e,emit:s}){const n=s,{t:a}=Pe(),{messages:l,isLoading:i,streamingMessageId:h,addMessage:g,updateMessage:u,startStreaming:d,stopStreaming:b,findMessage:R}=Om(),M=N(null),{scrollToBottom:_,autoScrollToBottom:y,showScrollToBottom:v}=qm(M),$=N(),C=x=>h.value===x,S=de(()=>l.value.map(Bm)),I=()=>{},m=x=>{const B=x.target;if(B.classList.contains("md-copy-btn")){const J=B.getAttribute("data-raw");if(J){const ee=decodeURIComponent(J);navigator.clipboard.writeText(ee).then(()=>{B.textContent=a("chat.copied"),setTimeout(()=>{B.textContent=a("chat.copy")},1e3)})}}},O=async x=>{const B=R(x);if(B)try{const J=B.content.replace(/<[^>]*>/g,"");await navigator.clipboard.writeText(J)}catch(J){console.error("Failed to copy message:",J)}},D=x=>{const B=R(x);B&&B.type==="assistant"&&(u(x,{content:""}),d(x))},z=x=>{R(x)&&(u(x,{content:""}),d(x))},k=x=>{console.log("[ChatContainer] Step selected:",x),n("step-selected",x)},F=x=>{console.log("[ChatContainer] Plan update received:",x);const B=Fe.getCachedPlanRecord(x);if(!B){console.warn("[ChatContainer] No cached plan data found for rootPlanId:",x);return}console.log("[ChatContainer] Retrieved plan details from cache:",B);const J=l.value.findIndex(ee=>{var Z;return((Z=ee.planExecution)==null?void 0:Z.currentPlanId)===B.currentPlanId&&ee.type==="assistant"});if(J!==-1){const ee=l.value[J],Z={planExecution:JSON.parse(JSON.stringify(B))};if(!B.agentExecutionSequence||B.agentExecutionSequence.length===0){if(console.log("[ChatContainer] Handling simple response without agent execution sequence"),B.completed){Z.thinking="";const he=B.summary??B.result??B.message??"Execution completed";Z.content=he,console.log("[ChatContainer] Set simple response content:",he)}}else console.log("[ChatContainer] Handling detailed plan with agent execution sequence");u(ee.id,Z)}},W=x=>{if(console.log("[ChatContainer] Plan completed:",x),x.rootPlanId){const B=l.value.findIndex(J=>{var ee;return((ee=J.planExecution)==null?void 0:ee.currentPlanId)===x.rootPlanId});if(B!==-1){const J=l.value[B],ee=x.summary??x.result??"Execution completed";u(J.id,{thinking:"",content:ee}),console.log("[ChatContainer] Updated completed message:",ee)}}},Q=x=>{console.log("[ChatContainer] Dialog round start:",x)},E=x=>{console.log("[ChatContainer] Plan error:",x),g("assistant",`Error: ${x}`),console.error("[ChatContainer] Plan execution error:",x)};return we(()=>{ce(l,()=>{ke(()=>{y()})},{deep:!0}),Fe.setEventCallbacks({onPlanUpdate:F,onPlanCompleted:W,onDialogRoundStart:Q,onPlanError:E})}),Te(()=>{$.value&&clearInterval($.value)}),e({scrollToBottom:_,handlePlanUpdate:F,handlePlanCompleted:W,handleDialogRoundStart:Q,handlePlanError:E,addMessage:g,updateMessage:u,startStreaming:d,stopStreaming:b}),(x,B)=>(p(),f("div",Vm,[t("div",{class:"messages",ref_key:"messagesRef",ref:M,onScroll:I,onClick:m},[(p(!0),f(ae,null,ie(S.value,J=>(p(),le(Um,{key:J.id,message:J,"is-streaming":C(J.id),onCopy:O,onRegenerate:D,onRetry:z,onStepSelected:k},null,8,["message","is-streaming"]))),128)),r(i)?(p(),f("div",Hm,[t("div",Gm,[w(r(P),{icon:"carbon:circle-dash",class:"loading-icon"}),t("span",null,c(x.$t("chat.processing")),1)])])):L("",!0)],544),w(qe,{name:"scroll-button"},{default:Me(()=>[r(v)?(p(),f("button",{key:0,class:"scroll-to-bottom",onClick:B[0]||(B[0]=()=>r(_)()),title:x.$t("chat.scrollToBottom")},[w(r(P),{icon:"carbon:chevron-down"})],8,zm)):L("",!0)]),_:1})]))}}),Jm=pe(Wm,[["__scopeId","data-v-e7a5d6c9"]]),wt=N([]),ss=o=>{wt.value=[...o]},Pt=()=>{wt.value=[]},Km=()=>wt.value,Xm=()=>wt.value.length>0;class Ym{static async deleteFile(e,s){try{console.log("[FileUploadApiService] Deleting file:",s,"from plan:",e);const n=await fetch(`/api/file-upload/files/${e}/${encodeURIComponent(s)}`,{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const a=await n.json();return console.log("[FileUploadApiService] File deleted successfully:",a),a}catch(n){throw console.error("[FileUploadApiService] Error deleting file:",n),n}}static async getUploadedFiles(e){try{console.log("[FileUploadApiService] Getting uploaded files for plan:",e);const s=await fetch(`/api/file-upload/files/${e}`);if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const n=await s.json();return console.log("[FileUploadApiService] Got uploaded files:",n.totalCount),n}catch(s){throw console.error("[FileUploadApiService] Error getting uploaded files:",s),s}}}const Qm={class:"input-area"},Zm={class:"input-container"},ef=["title"],tf=["placeholder","disabled"],sf=["title"],nf=["disabled","title"],of={key:0,class:"uploaded-files"},af={class:"files-header"},lf={class:"files-list"},rf={class:"file-name"},cf={class:"file-size"},df=["onClick","title"],uf={key:1,class:"upload-progress"},pf=ue({__name:"InputArea",props:{placeholder:{default:""},disabled:{type:Boolean,default:!1},initialValue:{default:""}},emits:["send","clear","update-state","plan-mode-clicked","files-uploaded"],setup(o,{expose:e,emit:s}){const{t:n}=Pe(),a=o,l=s,i=N(),h=N(),g=N(""),u=de(()=>a.placeholder||n("input.placeholder")),d=N(u.value),b=N([]),R=N(!1),M=N(null),_=()=>{M.value=null,b.value=[],Pt()};Te(()=>{_()}),ce(()=>b.value.length,(E,x)=>{});const y=de(()=>!!a.disabled),v=()=>{ke(()=>{i.value&&(i.value.style.height="auto",i.value.style.height=Math.min(i.value.scrollHeight,120)+"px")})},$=E=>{E.key==="Enter"&&!E.shiftKey&&(E.preventDefault(),C())},C=()=>{if(!g.value.trim()||y.value)return;let E=g.value.trim();if(b.value.length>0){const B=b.value.map(J=>`${J.name} (${J.relativePath})`).join(", ");E+=`

[Uploaded files: ${B}]`}const x={input:E,memoryId:Ne.selectMemoryId,uploadedFiles:b.value,sessionPlanId:M.value};l("send",x),k(),b.value=[],Pt()},S=()=>{l("plan-mode-clicked")},I=()=>{h.value&&h.value.click()},m=async E=>{const x=E.target,B=x.files;if(!B||B.length===0)return;const J=Array.from(B);console.log("[FileUpload] Selected files for upload:",J.map(ee=>ee.name)),await O(J),x&&(x.value="")},O=async E=>{if(E.length!==0){R.value=!0;try{const x=new FormData;E.forEach(Z=>{x.append("files",Z)});let B="/api/file-upload/upload";M.value?(B=`/api/file-upload/upload/${M.value}`,console.log("[FileUpload] Using existing sessionPlanId:",M.value)):console.log("[FileUpload] Creating new temporary planId");const J=await fetch(B,{method:"POST",body:x});if(!J.ok)throw new Error(`Upload failed: ${J.statusText}`);const ee=await J.json();if(ee.uploadedFiles){!M.value&&ee.planId?(M.value=ee.planId,console.log("[FileUpload] Set sessionPlanId:",M.value)):M.value&&console.log("[FileUpload] SessionPlanId already exists:",M.value);const Z=ee.uploadedFiles.map(he=>({name:he.originalName,size:he.size,type:he.extension,planId:ee.planId,relativePath:he.relativePath}));b.value=[...b.value,...Z],l("files-uploaded",Z),ss(b.value),console.log("[Input] Updated global uploadedFiles state:",b.value),b.value.length>0&&(d.value=n("input.filesAttached",{count:b.value.length}))}console.log("Files uploaded successfully:",ee)}catch(x){console.error("File upload error:",x)}finally{R.value=!1}}},D=async E=>{try{console.log("🗑️ Removing file:",E.name,"from plan:",E.planId),E.planId&&(await Ym.deleteFile(E.planId,E.name),console.log("✅ File deleted from server successfully")),b.value=b.value.filter(x=>x.name!==E.name),b.value.length===0?(d.value=u.value,Pt()):(d.value=n("input.filesAttached",{count:b.value.length}),ss(b.value)),console.log("🎉 File removal completed, remaining files:",b.value.length)}catch(x){console.error("❌ Error removing file:",x),alert(n("input.fileDeleteError")||"Failed to delete file")}},z=E=>{if(E===0)return"0 Bytes";const x=1024,B=["Bytes","KB","MB","GB"],J=Math.floor(Math.log(E)/Math.log(x));return parseFloat((E/Math.pow(x,J)).toFixed(2))+" "+B[J]},k=()=>{g.value="",v(),l("clear")},F=(E,x)=>{x&&(d.value=E?x:n("input.waiting")),l("update-state",E,x)},W=E=>{g.value=E,v()},Q=()=>g.value.trim();return ce(()=>a.initialValue,E=>{E&&E.trim()&&(g.value=E,v())},{immediate:!0}),e({clearInput:k,updateState:F,setInputValue:W,getQuery:Q,resetSession:_,focus:()=>{var E;return(E=i.value)==null?void 0:E.focus()}}),we(()=>{}),Te(()=>{}),(E,x)=>(p(),f("div",Qm,[t("div",Zm,[t("button",{class:"attach-btn",title:E.$t("input.attachFile"),onClick:I},[w(r(P),{icon:"carbon:attachment"})],8,ef),t("input",{ref_key:"fileInputRef",ref:h,type:"file",multiple:"",style:{display:"none"},onChange:m,accept:".pdf,.txt,.md,.doc,.docx,.csv,.xlsx,.xls,.json,.xml,.html,.htm,.log,.java,.py,.js,.ts,.sql,.sh,.bat,.yaml,.yml,.properties,.conf,.ini"},null,544),se(t("textarea",{"onUpdate:modelValue":x[0]||(x[0]=B=>g.value=B),ref_key:"inputRef",ref:i,class:"chat-input",placeholder:d.value,disabled:y.value,onKeydown:$,onInput:v},null,40,tf),[[re,g.value]]),t("button",{class:"plan-mode-btn",title:E.$t("input.planMode"),onClick:S},[w(r(P),{icon:"carbon:document"}),ne(" "+c(E.$t("input.planMode")),1)],8,sf),t("button",{class:"send-button",disabled:!g.value.trim()||y.value,onClick:C,title:E.$t("input.send")},[w(r(P),{icon:"carbon:send-alt"}),ne(" "+c(E.$t("input.send")),1)],8,nf)]),b.value.length>0?(p(),f("div",of,[t("div",af,[w(r(P),{icon:"carbon:document"}),t("span",null,c(r(n)("input.attachedFiles"))+" ("+c(b.value.length)+")",1)]),t("div",lf,[(p(!0),f(ae,null,ie(b.value,B=>(p(),f("div",{key:B.name,class:"file-item"},[w(r(P),{icon:"carbon:document",class:"file-icon"}),t("span",rf,c(B.name),1),t("span",cf,"("+c(z(B.size))+")",1),t("button",{onClick:J=>D(B),class:"remove-btn",title:r(n)("input.removeFile")},[w(r(P),{icon:"carbon:close"})],8,df)]))),128))])])):L("",!0),R.value?(p(),f("div",uf,[w(r(P),{icon:"carbon:rotate--clockwise",class:"loading-icon"}),t("span",null,c(r(n)("input.uploading")),1)])):L("",!0)]))}}),hf=pe(pf,[["__scopeId","data-v-6783ceee"]]);class He{static async getAllCronTasks(){try{const e=await fetch(this.BASE_URL);return await(await this.handleResponse(e)).json()}catch(e){throw console.error("Failed to get cron tasks:",e),e}}static async getCronTaskById(e){try{const s=await fetch(`${this.BASE_URL}/${e}`);return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to get cron task by id:",s),s}}static async createCronTask(e){try{const s=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to create cron task:",s),s}}static async updateCronTask(e,s){try{const n=await fetch(`${this.BASE_URL}/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to update cron task:",n),n}}static async updateTaskStatus(e,s){try{const n=await fetch(`${this.BASE_URL}/${e}/status?status=${s}`,{method:"PUT"});await this.handleResponse(n)}catch(n){throw console.error("Failed to update task status:",n),n}}static async deleteCronTask(e){try{const s=await fetch(`${this.BASE_URL}/${e}`,{method:"DELETE"});await this.handleResponse(s)}catch(s){throw console.error("Failed to delete cron task:",s),s}}static async handleResponse(e){if(!e.ok)try{const s=await e.json();throw new Error(s.message||`API request failed: ${e.status}`)}catch{throw new Error(`API request failed: ${e.status} ${e.statusText}`)}return e}}ge(He,"BASE_URL","/api/cron-tasks");const Ge={validateCronExpression(o){const e=o.trim().split(/\s+/);return e.length>=5&&e.length<=6},formatTime(o){return new Date(o).toLocaleString()},async saveTask(o){try{let e;return o.id?e=await He.updateCronTask(Number(o.id),o):e=await He.createCronTask(o),e}catch(e){throw console.error("Failed to save cron task:",e),e}},async deleteTask(o){try{await He.deleteCronTask(String(o))}catch(e){throw console.error("Failed to delete cron task:",e),e}},async toggleTaskStatus(o){if(!o.id)throw new Error("Task ID is required");const e=o.status===0?1:0;return await He.updateCronTask(Number(o.id),{...o,status:e})},prepareTaskExecution(o){return o.planTemplateId?{useTemplate:!0,planData:{title:o.cronName||"Scheduled Task Execution",planData:{id:o.planTemplateId,planTemplateId:o.planTemplateId,planId:o.planTemplateId},params:o.executionParams||void 0}}:{useTemplate:!1,taskContent:o.planDesc||o.cronName||""}}},mf={class:"modal-header"},ff={class:"header-actions"},vf={class:"status-switch"},gf={class:"status-label"},bf={class:"toggle-switch"},yf=["checked"],_f={class:"modal-content"},$f={class:"form-group"},wf={class:"form-label"},Sf=["placeholder"],kf={class:"form-group"},Tf={class:"form-label"},Pf=["placeholder"],Ef={class:"form-help"},Cf={class:"form-group"},If={class:"form-label"},Rf=["placeholder"],Af={class:"form-group"},xf={class:"form-label"},Df={class:"template-toggle"},Mf={key:0,class:"template-selector"},Ff={value:""},Nf=["value"],Uf={class:"form-help"},Lf={key:0,class:"form-group"},Bf={class:"time-info"},Of={class:"time-label"},qf={class:"time-value"},jf={key:1,class:"form-group"},Vf={class:"time-info"},Hf={class:"time-label"},Gf={class:"time-value"},zf={class:"modal-footer"},Wf=["disabled"],Jf=ue({__name:"TaskDetailModal",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue","save"],setup(o,{emit:e}){const s=o,n=e,a=N(!1),l=N([]),i=N({cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""}),h=async()=>{try{const _=await et.getAllPlanTemplates();_&&_.templates&&(l.value=_.templates.map(y=>({id:y.id,name:y.title||"Unnamed Template"})))}catch(_){console.error("Failed to get template list:",_)}},g=_=>{_.key==="Escape"&&s.modelValue&&n("update:modelValue",!1)};we(()=>{h(),document.addEventListener("keydown",g)}),Te(()=>{document.removeEventListener("keydown",g)});const u=_=>{_.target===_.currentTarget&&n("update:modelValue",!1)},d=()=>{i.value.linkTemplate=!1,i.value.templateId="",i.value.planTemplateId=""},b=()=>i.value.cronName.trim()?i.value.cronTime.trim()?Ge.validateCronExpression(i.value.cronTime)?i.value.planDesc.trim()?i.value.linkTemplate&&!i.value.templateId?(alert("Please select a plan template"),!1):!0:(alert("Task description cannot be empty"),!1):(alert("Invalid Cron expression format, should be 5-6 parts separated by spaces"),!1):(alert("Cron expression cannot be empty"),!1):(alert("Task name cannot be empty"),!1),R=_=>Ge.formatTime(_),M=async()=>{var _;if(b()){a.value=!0;try{const y={...i.value,...((_=s.task)==null?void 0:_.id)!==void 0&&{id:s.task.id},cronName:i.value.cronName.trim(),cronTime:i.value.cronTime.trim(),planDesc:i.value.planDesc.trim(),status:i.value.status,planTemplateId:i.value.linkTemplate&&i.value.templateId||""};n("save",y)}finally{a.value=!1}}};return ce(()=>s.task,_=>{if(_){const y=_.templateId||_.planTemplateId||"";i.value={cronName:_.cronName||"",cronTime:_.cronTime||"",planDesc:_.planDesc||"",status:_.status??1,linkTemplate:!!y,templateId:y,planTemplateId:y}}else i.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""}},{immediate:!0}),ce(()=>s.modelValue,_=>{_||(i.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""})}),(_,y)=>(p(),le(Qe,{to:"body"},[w(qe,{name:"modal"},{default:Me(()=>{var v,$,C;return[_.modelValue?(p(),f("div",{key:0,class:"modal-overlay",onClick:u},[t("div",{class:"modal-container",onClick:y[8]||(y[8]=me(()=>{},["stop"]))},[t("div",mf,[t("h3",null,c(_.$t("cronTask.taskDetail")),1),t("div",ff,[t("div",vf,[t("span",gf,c(_.$t("cronTask.taskStatus")),1),t("label",bf,[t("input",{type:"checkbox",checked:i.value.status===0,onChange:y[0]||(y[0]=S=>i.value.status=i.value.status===0?1:0)},null,40,yf),y[9]||(y[9]=t("span",{class:"toggle-slider"},null,-1))])]),t("button",{class:"close-btn",onClick:y[1]||(y[1]=S=>_.$emit("update:modelValue",!1))},[w(r(P),{icon:"carbon:close"})])])]),t("div",_f,[t("form",{onSubmit:me(M,["prevent"]),class:"task-form"},[t("div",$f,[t("label",wf,c(_.$t("cronTask.taskName")),1),se(t("input",{"onUpdate:modelValue":y[2]||(y[2]=S=>i.value.cronName=S),type:"text",class:"form-input",placeholder:_.$t("cronTask.taskNamePlaceholder"),required:""},null,8,Sf),[[re,i.value.cronName]])]),t("div",kf,[t("label",Tf,c(_.$t("cronTask.cronExpression")),1),se(t("input",{"onUpdate:modelValue":y[3]||(y[3]=S=>i.value.cronTime=S),type:"text",class:"form-input",placeholder:_.$t("cronTask.cronExpressionPlaceholder"),required:""},null,8,Pf),[[re,i.value.cronTime]]),t("div",Ef,c(_.$t("cronTask.cronExpressionHelp")),1)]),t("div",Cf,[t("label",If,c(_.$t("cronTask.taskDescription")),1),se(t("textarea",{"onUpdate:modelValue":y[4]||(y[4]=S=>i.value.planDesc=S),class:"form-textarea",placeholder:_.$t("cronTask.taskDescriptionPlaceholder"),rows:"4",required:""},null,8,Rf),[[re,i.value.planDesc]])]),t("div",Af,[t("label",xf,c(_.$t("cronTask.planTemplate")),1),t("div",Df,[t("button",{type:"button",class:oe(["template-btn",i.value.linkTemplate?"active":""]),onClick:y[5]||(y[5]=S=>i.value.linkTemplate=!0)},[w(r(P),{icon:"carbon:checkmark"}),ne(" "+c(_.$t("cronTask.linkTemplate")),1)],2),t("button",{type:"button",class:oe(["template-btn",i.value.linkTemplate?"":"active"]),onClick:d},[w(r(P),{icon:"carbon:close"}),ne(" "+c(_.$t("cronTask.noTemplate")),1)],2)]),i.value.linkTemplate?(p(),f("div",Mf,[se(t("select",{"onUpdate:modelValue":y[6]||(y[6]=S=>i.value.templateId=S),class:"form-select"},[t("option",Ff,c(_.$t("cronTask.selectTemplate")),1),(p(!0),f(ae,null,ie(l.value,S=>(p(),f("option",{key:S.id,value:S.id},c(S.name),9,Nf))),128))],512),[[nt,i.value.templateId]]),t("div",Uf,c(_.$t("cronTask.templateHelpText")),1)])):L("",!0)]),(v=_.task)!=null&&v.createTime?(p(),f("div",Lf,[t("div",Bf,[t("span",Of,c(_.$t("cronTask.createTime"))+":",1),t("span",qf,c(R(_.task.createTime)),1)])])):L("",!0),($=_.task)!=null&&$.updateTime?(p(),f("div",jf,[t("div",Vf,[t("span",Hf,c(_.$t("cronTask.updateTime"))+":",1),t("span",Gf,c(R(_.task.updateTime)),1)])])):L("",!0)],32)]),t("div",zf,[t("button",{type:"button",class:"cancel-btn",onClick:y[7]||(y[7]=S=>_.$emit("update:modelValue",!1))},c(_.$t("common.cancel")),1),t("button",{type:"button",class:"save-btn",onClick:M,disabled:a.value},[a.value?(p(),le(r(P),{key:0,icon:"carbon:loading",class:"loading-icon"})):L("",!0),ne(" "+c((C=s.task)!=null&&C.id?_.$t("common.save"):_.$t("common.create")),1)],8,Wf)])])])):L("",!0)]}),_:1})]))}}),Kf=pe(Jf,[["__scopeId","data-v-45bbcf14"]]),Xf={class:"modal-header"},Yf={class:"header-actions"},Qf={class:"modal-content"},Zf={key:0,class:"loading-container"},ev={key:1,class:"empty-container"},tv={key:2,class:"task-list"},sv=["onClick"],nv={class:"task-main"},ov={class:"task-info"},av={class:"task-header"},lv={class:"task-name"},rv={class:"task-description"},iv={class:"task-time"},cv=["onClick"],dv=["onClick","disabled","title"],uv=["onClick","title"],pv={class:"dropdown-menu"},hv=["onClick"],mv=["onClick","disabled"],fv=["onClick","disabled"],vv={class:"confirm-header"},gv={class:"confirm-content"},bv={class:"confirm-actions"},yv=["disabled"],_v={class:"confirm-header"},$v={class:"confirm-content"},wv={class:"create-options"},Sv={class:"option-content"},kv={class:"option-title"},Tv={class:"option-desc"},Pv={class:"option-content"},Ev={class:"option-title"},Cv={class:"option-desc"},Iv={class:"confirm-actions"},Rv=ue({__name:"CronTaskModal",props:{modelValue:{type:Boolean,required:!0}},emits:["update:modelValue"],setup(o,{emit:e}){const s=rs(),n=is(),a=cs(),{t:l}=Pe(),i=o,h=e,g=N([]),u=N(!1),d=N(null),b=N(null),R=N(null),M=N(null),_=N(!1),y=N(null),v=N(!1),$=N(null),C=N(!1),S=G=>{G.target===G.currentTarget&&h("update:modelValue",!1)},I=async()=>{u.value=!0;try{g.value=await He.getAllCronTasks()}catch(G){console.error("Failed to load cron tasks:",G),a.error(`Failed to load tasks: ${G instanceof Error?G.message:String(G)}`)}finally{u.value=!1}},m=async G=>{d.value=G;try{const T=g.value.find(q=>q.id===G);if(!T){console.error("Task not found:",G);return}h("update:modelValue",!1);const j=Date.now().toString();await s.push({name:"direct",params:{id:j}}),await new Promise(q=>setTimeout(q,100));const U=Ge.prepareTaskExecution(T);U.useTemplate&&U.planData?n.emitPlanExecutionRequested(U.planData):U.taskContent&&n.setTask(U.taskContent)}catch(T){console.error("Failed to execute task:",T),a.error(`Execution failed: ${T instanceof Error?T.message:String(T)}`)}finally{d.value=null}},O=G=>{y.value={...G},_.value=!0,M.value=null},D=async G=>{try{await Ge.saveTask(G),await I(),_.value=!1,a.success("Task saved successfully")}catch(T){console.error("Failed to save task:",T),a.error(`Save failed: ${T instanceof Error?T.message:String(T)}`)}},z=G=>{$.value=G,v.value=!0},k=async()=>{var G;if((G=$.value)!=null&&G.id){b.value=$.value.id;try{await Ge.deleteTask($.value.id),await I(),v.value=!1,$.value=null,a.success("Task deleted successfully")}catch(T){console.error("Failed to delete task:",T),a.error(`Delete failed: ${T instanceof Error?T.message:String(T)}`)}finally{b.value=null}}},F=()=>{v.value=!1,$.value=null},W=G=>{M.value=M.value===G?null:G},Q=async G=>{if(G.id){R.value=G.id;try{await Ge.toggleTaskStatus(G),await I(),M.value=null,a.success(`Task ${G.status===0?"disabled":"enabled"} successfully`)}catch(T){console.error("Failed to toggle task status:",T),a.error(`Status toggle failed: ${T instanceof Error?T.message:String(T)}`)}finally{R.value=null}}},E=async G=>{try{await navigator.clipboard.writeText(G),a.success("Cron expression copied successfully")}catch(T){a.error(`Copy failed: ${T instanceof Error?T.message:String(T)}`)}},x=()=>{C.value=!0},B=()=>{C.value=!1;try{h("update:modelValue",!1);const G=l("cronTask.template");n.setTaskToInput(G);const T=Date.now().toString();s.push({name:"direct",params:{id:T}})}catch(G){console.error("Error in createWithJmanus:",G),a.error(`Creation failed: ${G instanceof Error?G.message:String(G)}`)}},J=()=>{C.value=!1,y.value={cronName:"",cronTime:"",planDesc:"",status:0,planTemplateId:""},_.value=!0},ee=()=>{C.value=!1},Z=G=>{const T=G.target;!T.closest(".action-dropdown")&&!T.closest(".dropdown-menu")&&(M.value=null)},he=G=>{G.key==="Escape"&&(v.value?F():C.value?ee():i.modelValue&&h("update:modelValue",!1))};return we(()=>{document.addEventListener("click",Z,!0),document.addEventListener("keydown",he)}),Te(()=>{document.removeEventListener("click",Z,!0),document.removeEventListener("keydown",he)}),ce(()=>i.modelValue,G=>{G&&I()}),(G,T)=>(p(),f(ae,null,[(p(),le(Qe,{to:"body"},[w(qe,{name:"modal"},{default:Me(()=>[o.modelValue?(p(),f("div",{key:0,class:"modal-overlay",onClick:S},[t("div",{class:"modal-container",onClick:T[3]||(T[3]=me(()=>{},["stop"]))},[t("div",Xf,[t("h3",null,c(G.$t("cronTask.title")),1),t("div",Yf,[t("button",{class:"add-task-btn",onClick:[x,T[0]||(T[0]=me(()=>{},["stop"]))]},[w(r(P),{icon:"carbon:add"}),ne(" "+c(G.$t("cronTask.addTask")),1)]),t("button",{class:"close-btn",onClick:T[1]||(T[1]=j=>G.$emit("update:modelValue",!1))},[w(r(P),{icon:"carbon:close"})])])]),t("div",Qf,[u.value?(p(),f("div",Zf,[w(r(P),{icon:"carbon:loading",class:"loading-icon"}),t("span",null,c(G.$t("common.loading")),1)])):g.value.length===0?(p(),f("div",ev,[w(r(P),{icon:"carbon:time",class:"empty-icon"}),t("span",null,c(G.$t("cronTask.noTasks")),1)])):(p(),f("div",tv,[(p(!0),f(ae,null,ie(g.value,j=>(p(),f("div",{key:j.id||"",class:"task-item",onClick:U=>O(j)},[t("div",nv,[t("div",ov,[t("div",av,[t("div",lv,c(j.cronName),1),t("div",{class:oe(["task-status-badge",j.status===0?"active":"inactive"])},[w(r(P),{icon:j.status===0?"carbon:checkmark-filled":"carbon:pause-filled"},null,8,["icon"]),t("span",null,c(j.status===0?G.$t("cronTask.active"):G.$t("cronTask.inactive")),1)],2)]),t("div",rv,c(j.planDesc),1),t("div",iv,[w(r(P),{icon:"carbon:time"}),t("span",{class:"cron-readable",style:{cursor:"pointer"},onClick:me(U=>E(j.cronTime),["stop"])},c(j.cronTime),9,cv)])])]),t("div",{class:"task-actions",onClick:T[2]||(T[2]=me(()=>{},["stop"]))},[t("button",{class:"action-btn execute-btn",onClick:U=>m(j.id),disabled:d.value===j.id,title:G.$t("cronTask.executeOnce")},[w(r(P),{icon:d.value===j.id?"carbon:loading":"carbon:play-filled"},null,8,["icon"]),ne(" "+c(G.$t("cronTask.executeOnce")),1)],8,dv),t("div",{class:oe(["action-dropdown",{active:M.value===j.id}])},[t("button",{class:"action-btn dropdown-btn",onClick:U=>W(j.id),title:G.$t("cronTask.operations")},[w(r(P),{icon:"carbon:overflow-menu-horizontal"}),ne(" "+c(G.$t("cronTask.operations")),1)],8,uv),se(t("div",pv,[t("button",{class:"dropdown-item edit-btn",onClick:U=>O(j)},[w(r(P),{icon:"carbon:edit"}),ne(" "+c(G.$t("cronTask.edit")),1)],8,hv),t("button",{class:"dropdown-item toggle-btn",onClick:U=>Q(j),disabled:R.value===j.id},[w(r(P),{icon:R.value===j.id?"carbon:loading":j.status===0?"carbon:pause-filled":"carbon:play-filled"},null,8,["icon"]),ne(" "+c(j.status===0?G.$t("cronTask.disable"):G.$t("cronTask.enable")),1)],8,mv),t("button",{class:"dropdown-item delete-btn",onClick:U=>z(j),disabled:b.value===j.id},[w(r(P),{icon:b.value===j.id?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),ne(" "+c(G.$t("cronTask.delete")),1)],8,fv)],512),[[os,M.value===j.id]])],2)])],8,sv))),128))]))])])])):L("",!0)]),_:1})])),w(Kf,{modelValue:_.value,"onUpdate:modelValue":T[4]||(T[4]=j=>_.value=j),task:y.value,onSave:D},null,8,["modelValue","task"]),(p(),le(Qe,{to:"body"},[w(qe,{name:"modal"},{default:Me(()=>{var j,U,q,K;return[v.value?(p(),f("div",{key:0,class:"modal-overlay",onClick:F},[t("div",{class:"confirm-modal",onClick:T[5]||(T[5]=me(()=>{},["stop"]))},[t("div",vv,[w(r(P),{icon:"carbon:warning",class:"warning-icon"}),t("h3",null,c(G.$t("cronTask.deleteConfirm")),1)]),t("div",gv,[t("p",null,c(G.$t("cronTask.deleteConfirmMessage",{taskName:((j=$.value)==null?void 0:j.cronName)||((U=$.value)==null?void 0:U.planDesc)||""})),1)]),t("div",bv,[t("button",{class:"confirm-btn cancel-btn",onClick:F},c(G.$t("common.cancel")),1),t("button",{class:"confirm-btn delete-btn",onClick:k,disabled:b.value===((q=$.value)==null?void 0:q.id)},[w(r(P),{icon:b.value===((K=$.value)==null?void 0:K.id)?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),ne(" "+c(G.$t("cronTask.delete")),1)],8,yv)])])])):L("",!0)]}),_:1})])),(p(),le(Qe,{to:"body"},[w(qe,{name:"modal"},{default:Me(()=>[C.value?(p(),f("div",{key:0,class:"modal-overlay",onClick:ee},[t("div",{class:"confirm-modal create-options-modal",onClick:T[6]||(T[6]=me(()=>{},["stop"]))},[t("div",_v,[w(r(P),{icon:"carbon:time",class:"create-icon"}),t("h3",null,c(G.$t("cronTask.createTask")),1)]),t("div",$v,[t("p",null,c(G.$t("cronTask.selectCreateMethod")),1),t("div",wv,[t("button",{class:"create-option-btn jmanus-btn",onClick:B},[w(r(P),{icon:"carbon:ai-status"}),t("div",Sv,[t("span",kv,c(G.$t("cronTask.createWithJmanus")),1),t("span",Tv,c(G.$t("cronTask.createWithJmanusDesc")),1)])]),t("button",{class:"create-option-btn manual-btn",onClick:J},[w(r(P),{icon:"carbon:edit"}),t("div",Pv,[t("span",Ev,c(G.$t("cronTask.createManually")),1),t("span",Cv,c(G.$t("cronTask.createManuallyDesc")),1)])])])]),t("div",Iv,[t("button",{class:"confirm-btn cancel-btn",onClick:ee},c(G.$t("common.cancel")),1)])])])):L("",!0)]),_:1})]))],64))}}),Av=pe(Rv,[["__scopeId","data-v-a90a3961"]]),xv={class:"direct-page"},Dv={class:"direct-chat"},Mv={class:"chat-header"},Fv={class:"header-actions"},Nv=["title"],Uv=["title"],Lv=["title"],Bv=["title"],Ov={class:"chat-content"},qv=["title"],jv={class:"message-content"},Vv=ue({__name:"index",setup(o){const e=qs(),s=rs(),n=is(),{t:a}=Pe(),{message:l}=zs(),i=N(""),h=N(""),g=N(),u=N(),d=N(),b=N(),R=N(!1),M=N(!1),_=N(null),y=N(!1),v=N(50),$=N(!1),C=N(0),S=N(0),I=de(()=>{if(H.isCollapsed)return v.value;let U=26;b.value&&b.value.sidebarWidth!==void 0&&(U=b.value.sidebarWidth);const q=100-U;return Math.max(20,Math.min(q,v.value))});we(()=>{if(console.log("[Direct] onMounted called"),console.log("[Direct] taskStore.currentTask:",n.currentTask),console.log("[Direct] taskStore.hasUnprocessedTask():",n.hasUnprocessedTask()),Fe.setEventCallbacks({onPlanUpdate:q=>{console.log("[Direct] Plan update event received for rootPlanId:",q),k(q)&&(console.log("[Direct] Processing plan update for current rootPlanId:",q),u.value&&typeof u.value.handlePlanUpdate=="function"?(console.log("[Direct] Calling chatRef.handlePlanUpdate with rootPlanId:",q),u.value.handlePlanUpdate(q)):console.warn("[Direct] chatRef.handlePlanUpdate method not available"),g.value&&typeof g.value.updateDisplayedPlanProgress=="function"?(console.log("[Direct] Calling rightPanelRef.updateDisplayedPlanProgress with rootPlanId:",q),g.value.updateDisplayedPlanProgress(q)):console.warn("[Direct] rightPanelRef.updateDisplayedPlanProgress method not available"))},onPlanCompleted:q=>{if(console.log("[Direct] Plan completed event received for rootPlanId:",q),!!k(q)){if(console.log("[Direct] Processing plan completion for current rootPlanId:",q),u.value&&typeof u.value.handlePlanCompleted=="function"){const K=Fe.getCachedPlanRecord(q);console.log("[Direct] Calling chatRef.handlePlanCompleted with details:",K),u.value.handlePlanCompleted(K??{planId:q})}else console.warn("[Direct] chatRef.handlePlanCompleted method not available");_.value=null,console.log("[Direct] Cleared currentRootPlanId after plan completion")}},onDialogRoundStart:q=>{console.log("[Direct] Dialog round start event received for rootPlanId:",q),_.value=q,console.log("[Direct] Set currentRootPlanId to:",q),u.value&&typeof u.value.handleDialogRoundStart=="function"?(console.log("[Direct] Calling chatRef.handleDialogRoundStart with planId:",q),u.value.handleDialogRoundStart(q)):console.warn("[Direct] chatRef.handleDialogRoundStart method not available")},onChatInputClear:()=>{console.log("[Direct] Chat input clear event received"),Q()},onChatInputUpdateState:q=>{if(console.log("[Direct] Chat input update state event received for rootPlanId:",q),!k(q,!0))return;const K=Fe.getCachedUIState(q);K&&x(K.enabled,K.placeholder)},onPlanError:q=>{u.value.handlePlanError(q)}}),console.log("[Direct] Event callbacks registered to planExecutionManager"),H.loadPlanTemplateList(),n.hasUnprocessedTask()&&n.currentTask){const q=n.currentTask.prompt;console.log("[Direct] Found unprocessed task from store:",q),n.markTaskAsProcessed(),ke(async()=>{try{console.log("[Direct] Calling handleChatSendMessage with taskContent:",q),await F({input:q})}catch(K){console.warn("[Direct] handleChatSendMessage failed, falling back to prompt:",K),i.value=q}})}else{const q=n.getAndClearTaskToInput();q?(h.value=q,console.log("[Direct] Setting inputOnlyContent for input only:",h.value)):(i.value=e.query.prompt||"",console.log("[Direct] Received task from URL:",i.value),console.log("[Direct] No unprocessed task in store"))}const U=localStorage.getItem("directPanelWidth");U&&(v.value=parseFloat(U)),console.log("[Direct] Final prompt value:",i.value),h.value&&ke(()=>{d.value&&typeof d.value.setInputValue=="function"&&(d.value.setInputValue(h.value),console.log("[Direct] Set input value:",h.value),h.value="")}),window.addEventListener("plan-execution-requested",q=>{console.log("[DirectView] Received plan-execution-requested event:",q.detail),G(q.detail)})}),ce(()=>n.currentTask,U=>{if(console.log("[Direct] Watch taskStore.currentTask triggered, newTask:",U),U&&!U.processed){const q=U.prompt;n.markTaskAsProcessed(),console.log("[Direct] Received new task from store:",q),ke(async()=>{try{console.log("[Direct] Directly executing new task via handleChatSendMessage:",q),await F({input:q})}catch(K){console.warn("[Direct] handleChatSendMessage failed for new task:",K)}})}else console.log("[Direct] Task is null or already processed, ignoring")},{immediate:!1}),ce(()=>i.value,(U,q)=>{console.log("[Direct] prompt value changed from:",q,"to:",U)},{immediate:!1}),ce(()=>n.taskToInput,U=>{console.log("[Direct] Watch taskStore.taskToInput triggered, newTaskToInput:",U),U!=null&&U.trim()&&(console.log("[Direct] Setting input value from taskToInput:",U),ke(()=>{d.value&&typeof d.value.setInputValue=="function"&&(d.value.setInputValue(U.trim()),console.log("[Direct] Input value set from taskToInput watch:",U.trim()),n.getAndClearTaskToInput())}))},{immediate:!1}),Te(()=>{console.log("[Direct] onUnmounted called, cleaning up resources"),_.value=null,Fe.cleanup(),document.removeEventListener("mousemove",O),document.removeEventListener("mouseup",D),window.removeEventListener("plan-execution-requested",U=>{G(U.detail)})});const m=U=>{$.value=!0,C.value=U.clientX,S.value=v.value,document.addEventListener("mousemove",O),document.addEventListener("mouseup",D),document.body.style.cursor="col-resize",document.body.style.userSelect="none",U.preventDefault()},O=U=>{if(!$.value)return;const q=window.innerWidth,ve=(U.clientX-C.value)/q*100;let V=S.value+ve;V=Math.max(20,Math.min(80,V)),v.value=V},D=()=>{$.value=!1,document.removeEventListener("mousemove",O),document.removeEventListener("mouseup",D),document.body.style.cursor="",document.body.style.userSelect="",localStorage.setItem("directPanelWidth",v.value.toString())},z=()=>{v.value=50,localStorage.setItem("directPanelWidth","50")},k=(U,q=!1)=>!_.value||U===_.value||q&&(U==="ui-state"||U==="error")?!0:(console.log("[Direct] Ignoring event for non-current rootPlanId:",U,"current:",_.value),!1),F=async U=>{var K,ve,V,Y,X,ye,xe,Ke,Xe;let q=null;try{console.log("[DirectView] Processing send-message event:",U);const Re=(K=u.value)==null?void 0:K.addMessage("user",U.input);U.attachments&&Re&&((ve=u.value)==null||ve.updateMessage(Re.id,{attachments:U.attachments})),q=(V=u.value)==null?void 0:V.addMessage("assistant","",{thinking:a("chat.thinkingProcessing")}),q&&((Y=u.value)==null||Y.startStreaming(q.id));const{DirectApiService:lt}=await js(async()=>{const{DirectApiService:rt}=await Promise.resolve().then(()=>jm);return{DirectApiService:rt}},void 0);console.log("[DirectView] Calling DirectApiService.sendMessage");const Ee=await lt.sendMessage(U);console.log("[DirectView] API response received:",Ee),Ee.planId&&q?((X=u.value)==null||X.updateMessage(q.id,{thinking:a("chat.planningExecution"),planExecution:{currentPlanId:Ee.planId,rootPlanId:Ee.planId,status:"running"}}),_.value=Ee.planId,console.log("[DirectView] Set currentRootPlanId to:",Ee.planId),Fe.handlePlanExecutionRequested(Ee.planId,U.input),console.log("[DirectView] Started polling for plan execution updates")):q&&((ye=u.value)==null||ye.updateMessage(q.id,{content:Ee.message||Ee.result||"No response received from backend"}),(xe=u.value)==null||xe.stopStreaming(q.id))}catch(Re){console.error("[DirectView] Send message failed:",Re),(Ke=u.value)==null||Ke.addMessage("assistant",`Error: ${(Re==null?void 0:Re.message)||"Failed to send message"}`),q&&((Xe=u.value)==null||Xe.stopStreaming(q.id))}},W=async U=>{console.log("[DirectView] Send message from input:",JSON.stringify(U)),await F(U)},Q=()=>{console.log("[DirectView] Input cleared"),d.value&&typeof d.value.clear=="function"&&d.value.clear()},E=()=>{console.log("[DirectView] Input focused")},x=(U,q)=>{console.log("[DirectView] Input state updated:",U,q),M.value=!U},B=U=>{console.log("[DirectView] Step selected:",U),g.value&&typeof g.value.handleStepSelected=="function"?(console.log("[DirectView] Forwarding step selection to right panel:",U),g.value.handleStepSelected(U)):console.warn("[DirectView] rightPanelRef.handleStepSelected method not available")},J=(U,q,K,ve)=>{console.log("[DirectView] Sub plan step selected:",{parentPlanId:U,subPlanId:q,stepIndex:K,subStepIndex:ve}),g.value&&typeof g.value.handleSubPlanStepSelected=="function"?(console.log("[DirectView] Forwarding sub plan step selection to right panel:",{parentPlanId:U,subPlanId:q,stepIndex:K,subStepIndex:ve}),g.value.handleSubPlanStepSelected(U,q,K,ve)):console.warn("[DirectView] rightPanelRef.handleSubPlanStepSelected method not available")},ee=()=>{console.log("[DirectView] Plan mode button clicked"),H.toggleSidebar(),console.log("[DirectView] Sidebar toggled, isCollapsed:",H.isCollapsed)},Z=()=>{s.push("/home")},he=()=>{s.push("/configs")},G=async U=>{var ve,V,Y,X,ye,xe,Ke,Xe,Re,lt,Ee,rt;if(console.log("[DirectView] Plan execution requested:",U),R.value){console.log("[DirectView] Plan execution already in progress, ignoring request");return}R.value=!0;let q=!1,K=null;try{console.log("[DirectView] Adding messages for plan execution:",U.title),(ve=u.value)==null||ve.addMessage("user",U.title),q=!0,K=(V=u.value)==null?void 0:V.addMessage("assistant","",{thinking:a("chat.planningExecution")}),K&&((Y=u.value)==null||Y.startStreaming(K.id),console.log("[DirectView] Added assistant message for plan execution:",K.id))}catch(Se){console.warn("[DirectView] Failed to add messages:",Se)}try{const Se=((X=U.planData)==null?void 0:X.planTemplateId)||((ye=U.planData)==null?void 0:ye.id)||((xe=U.planData)==null?void 0:xe.planId);if(!Se)throw new Error(a("direct.planTemplateIdNotFound"));console.log("[Direct] Executing plan with templateId:",Se,"params:",U.params),console.log("[Direct] About to call PlanActApiService.executePlan");const Ue=Xm()?Km():void 0;console.log("[Direct] Executing with uploaded files:",(Ue==null?void 0:Ue.length)??0),console.log("[Direct] Executing with replacement params:",U.replacementParams);let Ce;if((Ke=U.params)!=null&&Ke.trim()?(console.log("[Direct] Calling executePlan with rawParam:",U.params.trim()),Ce=await et.executePlan(Se,U.params.trim(),Ue,U.replacementParams)):(console.log("[Direct] Calling executePlan without rawParam"),Ce=await et.executePlan(Se,void 0,Ue,U.replacementParams)),console.log("[Direct] Plan execution API response:",Ce),Ce.planId&&K)console.log("[Direct] Got planId from response:",Ce.planId,"starting plan execution"),(Xe=u.value)==null||Xe.updateMessage(K.id,{thinking:a("chat.planningExecution"),planExecution:{currentPlanId:Ce.planId,rootPlanId:Ce.planId,status:"running"}}),_.value=Ce.planId,console.log("[Direct] Set currentRootPlanId to:",Ce.planId),console.log("[Direct] Delegating plan execution to planExecutionManager"),Fe.handlePlanExecutionRequested(Ce.planId,U.title);else throw console.error("[Direct] No planId in response:",Ce),new Error(a("direct.executionFailedNoPlanId"))}catch(Se){console.error("[Direct] Plan execution failed:",Se),console.error("[Direct] Error details:",{message:Se.message,stack:Se.stack}),_.value=null;try{console.log("[Direct] Adding error messages to chat"),q||(Re=u.value)==null||Re.addMessage("user",U.title),K?((lt=u.value)==null||lt.updateMessage(K.id,{content:`${a("direct.executionFailed")}: ${Se.message||a("common.unknownError")}`,thinking:void 0}),(Ee=u.value)==null||Ee.stopStreaming(K.id)):(rt=u.value)==null||rt.addMessage("assistant",`${a("direct.executionFailed")}: ${Se.message||a("common.unknownError")}`)}catch(Ue){console.error("[Direct] Failed to add error messages:",Ue),alert(`${a("direct.executionFailed")}: ${Se.message||a("common.unknownError")}`)}}finally{console.log("[Direct] Plan execution finished, resetting isExecutingPlan flag"),R.value=!1}},T=()=>{u.value.showMemory()},j=()=>{Ne.clearMemoryId(),u.value.newChat()};return(U,q)=>(p(),f("div",xv,[t("div",Dv,[w(Wi,{ref_key:"sidebarRef",ref:b,onPlanExecutionRequested:G},null,512),t("div",{class:"left-panel",style:ze({width:I.value+"%"})},[t("div",Mv,[t("button",{class:"back-button",onClick:Z},[w(r(P),{icon:"carbon:arrow-left"})]),t("h2",null,c(U.$t("conversation")),1),t("div",Fv,[w(Js),t("button",{class:"config-button",onClick:j,title:U.$t("memory.newChat")},[w(r(P),{icon:"carbon:add",width:"20"})],8,Nv),t("button",{class:"config-button",onClick:he,title:U.$t("direct.configuration")},[w(r(P),{icon:"carbon:settings-adjust",width:"20"})],8,Uv),t("button",{class:"cron-task-btn",onClick:q[0]||(q[0]=K=>y.value=!0),title:U.$t("cronTask.title")},[w(r(P),{icon:"carbon:alarm",width:"20"})],8,Lv),t("button",{class:"cron-task-btn",onClick:q[1]||(q[1]=K=>r(Ne).toggleSidebar()),title:U.$t("memory.selectMemory")},[w(r(P),{icon:"carbon:calendar",width:"20"})],8,Bv)])]),t("div",Ov,[w(Jm,{ref_key:"chatRef",ref:u,mode:"direct","initial-prompt":i.value||"",onStepSelected:B,onSubPlanStepSelected:J},null,8,["initial-prompt"])]),(p(),le(hf,{key:U.$i18n.locale,ref_key:"inputRef",ref:d,disabled:M.value,placeholder:M.value?r(a)("input.waiting"):r(a)("input.placeholder"),"initial-value":i.value,onSend:W,onClear:Q,onFocus:E,onUpdateState:x,onPlanModeClicked:ee},null,8,["disabled","placeholder","initial-value"]))],4),t("div",{class:"panel-resizer",onMousedown:m,onDblclick:z,title:U.$t("direct.panelResizeHint")},q[3]||(q[3]=[t("div",{class:"resizer-line"},null,-1)]),40,qv),w(zu,{ref_key:"rightPanelRef",ref:g,style:ze({width:100-v.value+"%"}),"current-root-plan-id":_.value},null,8,["style","current-root-plan-id"])]),w(Av,{modelValue:y.value,"onUpdate:modelValue":q[2]||(q[2]=K=>y.value=K)},null,8,["modelValue"]),w(jc,{onMemorySelected:T}),r(l).show?(p(),f("div",{key:0,class:oe(["message-toast",r(l).type])},[t("div",jv,[t("span",null,c(r(l).text),1)])],2)):L("",!0)]))}}),fg=pe(Vv,[["__scopeId","data-v-0d21d333"]]);export{fg as default};
